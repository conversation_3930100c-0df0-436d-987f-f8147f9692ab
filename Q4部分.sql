-- =====================================================================
-- GigaGlow公司欺诈风险检测SQL查询集
-- PostgreSQL数据库课程作业 - Q4部分
-- 作者：数据库分析团队
-- 创建日期：2024年
-- 基于欺诈三角理论(Fraud Triangle)的综合风险评估
-- =====================================================================

-- =====================================================================
-- 欺诈检测1：自我批准违规检测
-- 风险类型：职责分离违规 (Segregation of Duties Violation)
-- 检测目标：识别员工自我推荐和自我批准的违规行为
-- 业务风险：违反内控原则，可能导致虚假交易和收入操纵
-- =====================================================================

-- 查询1：自我批准违规检测分析
WITH self_approval_violations AS (
    SELECT
        gs.emp_id,
        e.first_name || ' ' || e.last_name AS employee_name,
        jp.position_title,
        gs.referrer_emp_id,
        gs.glaze_sale_id,
        gs.sale_type,
        gs.sale_amount,
        gs.date_ordered,
        c.customer_name,
        c.suburb,
        -- 违规类型分类
        CASE 
            WHEN gs.emp_id = gs.referrer_emp_id THEN 'Self-Referral Violation'
            ELSE 'Normal Transaction'
        END AS violation_type,
        -- 风险等级评估
        CASE 
            WHEN gs.sale_amount > 10000 THEN 'High Risk'
            WHEN gs.sale_amount > 5000 THEN 'Medium Risk'
            WHEN gs.sale_amount > 1000 THEN 'Low Risk'
            ELSE 'Minimal Risk'
        END AS risk_level
    FROM glaze_sale gs
    INNER JOIN employee e ON gs.emp_id = e.emp_id
    INNER JOIN job_position jp ON e.job_position_id = jp.job_position_id
    INNER JOIN customer c ON gs.customer_id = c.customer_id
    WHERE gs.emp_id = gs.referrer_emp_id  -- 自我推荐违规条件
        AND gs.sale_amount > 0
),
violation_summary AS (
    SELECT 
        emp_id,
        employee_name,
        position_title,
        COUNT(*) AS self_approval_count,
        SUM(sale_amount) AS total_self_approved_amount,
        AVG(sale_amount) AS avg_self_approved_amount,
        MAX(sale_amount) AS max_self_approved_amount,
        MIN(date_ordered) AS first_violation_date,
        MAX(date_ordered) AS latest_violation_date,
        COUNT(DISTINCT suburb) AS affected_areas,
        COUNT(DISTINCT sale_type) AS violation_transaction_types,
        -- 风险评分计算
        SUM(CASE WHEN risk_level = 'High Risk' THEN 4
                 WHEN risk_level = 'Medium Risk' THEN 3
                 WHEN risk_level = 'Low Risk' THEN 2
                 ELSE 1 END) AS total_risk_score
    FROM self_approval_violations
    GROUP BY emp_id, employee_name, position_title
)
SELECT 
    'Self-Approval Violations' AS fraud_test_type,
    emp_id AS 员工ID,
    employee_name AS 员工姓名,
    position_title AS 职位,
    self_approval_count AS 自我批准次数,
    ROUND(total_self_approved_amount, 2) AS 涉及金额总计,
    ROUND(avg_self_approved_amount, 2) AS 平均违规金额,
    ROUND(max_self_approved_amount, 2) AS 最大违规金额,
    affected_areas AS 涉及区域数,
    violation_transaction_types AS 违规交易类型数,
    total_risk_score AS 风险评分,
    CASE 
        WHEN total_risk_score >= 15 THEN 'Critical Risk'
        WHEN total_risk_score >= 10 THEN 'High Risk'
        WHEN total_risk_score >= 5 THEN 'Medium Risk'
        ELSE 'Low Risk'
    END AS overall_risk_assessment,
    first_violation_date AS 首次违规日期,
    latest_violation_date AS 最近违规日期,
    EXTRACT(DAYS FROM (latest_violation_date - first_violation_date)) AS 违规时间跨度天数
FROM violation_summary
ORDER BY total_self_approved_amount DESC, self_approval_count DESC;

-- =====================================================================
-- 欺诈检测2：重复付款检测
-- 风险类型：重复付款欺诈 (Duplicate Payment Fraud)
-- 检测目标：识别相同金额、相同日期的可疑重复付款
-- 业务风险：供应商欺诈、内部串通、系统漏洞利用
-- =====================================================================

-- 查询2：重复付款检测分析
WITH duplicate_payment_analysis AS (
    SELECT 
        pm.amount_paid,
        pm.payment_date,
        COUNT(*) AS duplicate_count,
        SUM(pm.amount_paid) AS total_duplicate_amount,
        STRING_AGG(DISTINCT pm.payment_made_id::text, ', ') AS payment_ids,
        STRING_AGG(DISTINCT v.vendor_name, ', ') AS affected_vendors,
        STRING_AGG(DISTINCT e.first_name || ' ' || e.last_name, ', ') AS approving_employees,
        -- 风险指标计算
        AVG(pm.amount_paid) AS avg_payment_amount,
        MAX(pm.amount_paid) AS max_payment_amount,
        COUNT(DISTINCT v.vendor_id) AS unique_vendors_count,
        COUNT(DISTINCT pm.emp_id) AS unique_approvers_count
    FROM payment_made pm
    INNER JOIN vendor v ON pm.vendor_id = v.vendor_id
    LEFT JOIN employee e ON pm.emp_id = e.emp_id
    GROUP BY pm.amount_paid, pm.payment_date
    HAVING COUNT(*) > 1  -- 只显示重复付款
        AND pm.amount_paid > 100  -- 过滤小额付款
),
risk_assessment AS (
    SELECT 
        *,
        -- 风险等级评估
        CASE 
            WHEN total_duplicate_amount > 50000 THEN 'Critical Risk'
            WHEN total_duplicate_amount > 20000 THEN 'High Risk'
            WHEN total_duplicate_amount > 5000 THEN 'Medium Risk'
            ELSE 'Low Risk'
        END AS risk_level,
        -- 可疑程度评估
        CASE 
            WHEN unique_vendors_count = 1 AND unique_approvers_count = 1 THEN 'Highly Suspicious'
            WHEN unique_vendors_count = 1 THEN 'Moderately Suspicious'
            WHEN duplicate_count > 3 THEN 'Pattern Detected'
            ELSE 'Requires Investigation'
        END AS suspicion_level,
        -- 潜在损失计算
        (duplicate_count - 1) * amount_paid AS potential_loss
    FROM duplicate_payment_analysis
)
SELECT 
    'Duplicate Payments' AS fraud_test_type,
    ROUND(amount_paid, 2) AS 付款金额,
    payment_date AS 付款日期,
    duplicate_count AS 重复次数,
    ROUND(total_duplicate_amount, 2) AS 重复付款总额,
    ROUND(potential_loss, 2) AS 潜在损失,
    payment_ids AS 付款记录ID,
    affected_vendors AS 涉及供应商,
    approving_employees AS 批准员工,
    unique_vendors_count AS 涉及供应商数量,
    unique_approvers_count AS 批准员工数量,
    risk_level AS 风险等级,
    suspicion_level AS 可疑程度,
    CASE 
        WHEN unique_vendors_count = 1 AND unique_approvers_count = 1 THEN 
            '建议立即调查：同一供应商同一批准人的重复付款'
        WHEN duplicate_count > 5 THEN 
            '建议系统审查：异常高频重复付款模式'
        ELSE 
            '建议核实：可能的系统错误或操作失误'
    END AS 调查建议
FROM risk_assessment
ORDER BY total_duplicate_amount DESC, duplicate_count DESC;

-- =====================================================================
-- 欺诈检测3：异常高额交易检测
-- 风险类型：异常交易模式 (Anomalous Transaction Patterns)
-- 检测目标：使用统计分析识别异常高额交易
-- 业务风险：价格操纵、虚假交易、内部串通
-- =====================================================================

-- 查询3：异常高额交易统计分析
WITH sales_statistics AS (
    SELECT
        AVG(sale_amount) AS avg_amount,
        STDDEV(sale_amount) AS std_amount,
        PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY sale_amount) AS percentile_95,
        PERCENTILE_CONT(0.99) WITHIN GROUP (ORDER BY sale_amount) AS percentile_99,
        COUNT(*) AS total_transactions,
        MAX(sale_amount) AS max_amount,
        MIN(sale_amount) AS min_amount
    FROM glaze_sale
    WHERE sale_amount > 0
),
anomaly_detection AS (
    SELECT
        gs.glaze_sale_id,
        gs.sale_amount,
        gs.sale_type,
        gs.date_ordered,
        e.first_name || ' ' || e.last_name AS employee_name,
        jp.position_title,
        c.customer_name,
        c.suburb,
        -- Z-Score计算（标准化异常检测）
        ROUND((gs.sale_amount - ss.avg_amount) / NULLIF(ss.std_amount, 0), 2) AS z_score,
        -- 百分位排名
        ROUND(PERCENT_RANK() OVER (ORDER BY gs.sale_amount) * 100, 2) AS percentile_rank,
        -- 异常程度分类
        CASE 
            WHEN gs.sale_amount > (ss.avg_amount + 3 * ss.std_amount) THEN 'Extreme Outlier'
            WHEN gs.sale_amount > (ss.avg_amount + 2 * ss.std_amount) THEN 'Moderate Outlier'
            WHEN gs.sale_amount > ss.percentile_95 THEN 'High Value'
            ELSE 'Normal Range'
        END AS anomaly_classification,
        -- 风险评分
        CASE 
            WHEN gs.sale_amount > (ss.avg_amount + 3 * ss.std_amount) THEN 10
            WHEN gs.sale_amount > (ss.avg_amount + 2 * ss.std_amount) THEN 7
            WHEN gs.sale_amount > ss.percentile_99 THEN 5
            WHEN gs.sale_amount > ss.percentile_95 THEN 3
            ELSE 1
        END AS risk_score
    FROM glaze_sale gs
    CROSS JOIN sales_statistics ss
    INNER JOIN employee e ON gs.emp_id = e.emp_id
    INNER JOIN job_position jp ON e.job_position_id = jp.job_position_id
    INNER JOIN customer c ON gs.customer_id = c.customer_id
    WHERE gs.sale_amount > 0
),
high_risk_transactions AS (
    SELECT 
        *,
        -- 员工异常交易统计
        COUNT(*) OVER (PARTITION BY employee_name) AS employee_anomaly_count,
        SUM(sale_amount) OVER (PARTITION BY employee_name) AS employee_total_anomaly_amount,
        -- 客户异常交易统计
        COUNT(*) OVER (PARTITION BY customer_name) AS customer_anomaly_count,
        -- 时间模式分析
        EXTRACT(HOUR FROM date_ordered) AS transaction_hour,
        EXTRACT(DOW FROM date_ordered) AS day_of_week
    FROM anomaly_detection
    WHERE anomaly_classification IN ('Extreme Outlier', 'Moderate Outlier')
)
SELECT 
    'High Value Anomalies' AS fraud_test_type,
    glaze_sale_id AS 交易ID,
    ROUND(sale_amount, 2) AS 交易金额,
    sale_type AS 交易类型,
    date_ordered AS 交易日期,
    employee_name AS 处理员工,
    position_title AS 员工职位,
    customer_name AS 客户姓名,
    suburb AS 客户区域,
    z_score AS Z评分,
    percentile_rank AS 百分位排名,
    anomaly_classification AS 异常分类,
    risk_score AS 风险评分,
    employee_anomaly_count AS 员工异常交易数,
    ROUND(employee_total_anomaly_amount, 2) AS 员工异常交易总额,
    customer_anomaly_count AS 客户异常交易数,
    transaction_hour AS 交易小时,
    CASE 
        WHEN day_of_week = 0 THEN '周日'
        WHEN day_of_week = 1 THEN '周一'
        WHEN day_of_week = 2 THEN '周二'
        WHEN day_of_week = 3 THEN '周三'
        WHEN day_of_week = 4 THEN '周四'
        WHEN day_of_week = 5 THEN '周五'
        WHEN day_of_week = 6 THEN '周六'
    END AS 交易星期,
    CASE 
        WHEN employee_anomaly_count > 5 THEN '重点关注员工'
        WHEN customer_anomaly_count > 3 THEN '重点关注客户'
        WHEN z_score > 4 THEN '极端异常交易'
        ELSE '标准调查'
    END AS 调查优先级
FROM high_risk_transactions
ORDER BY sale_amount DESC, z_score DESC;

-- =====================================================================
-- 欺诈检测4：工作时间外交易检测
-- 风险类型：时间异常交易 (Off-Hours Transaction Anomalies)
-- 检测目标：识别非正常工作时间的可疑交易
-- 业务风险：未授权访问、内部人员滥用、系统安全漏洞
-- =====================================================================

-- 查询4：工作时间外交易检测分析
WITH off_hours_transactions AS (
    SELECT
        gs.glaze_sale_id,
        gs.sale_amount,
        gs.sale_type,
        gs.date_ordered,
        EXTRACT(HOUR FROM gs.date_ordered) AS transaction_hour,
        EXTRACT(DOW FROM gs.date_ordered) AS day_of_week,
        e.first_name || ' ' || e.last_name AS employee_name,
        jp.position_title,
        c.customer_name,
        c.suburb,
        -- 时间分类
        CASE
            WHEN EXTRACT(DOW FROM gs.date_ordered) IN (0, 6) THEN 'Weekend'
            WHEN EXTRACT(HOUR FROM gs.date_ordered) < 8 OR EXTRACT(HOUR FROM gs.date_ordered) > 18 THEN 'After Hours'
            ELSE 'Business Hours'
        END AS time_classification,
        -- 风险等级评估
        CASE
            WHEN EXTRACT(DOW FROM gs.date_ordered) IN (0, 6) AND gs.sale_amount > 10000 THEN 'Critical Risk'
            WHEN EXTRACT(HOUR FROM gs.date_ordered) IN (0, 1, 2, 3, 4, 5) AND gs.sale_amount > 5000 THEN 'High Risk'
            WHEN EXTRACT(HOUR FROM gs.date_ordered) NOT BETWEEN 8 AND 18 AND gs.sale_amount > 1000 THEN 'Medium Risk'
            ELSE 'Low Risk'
        END AS risk_level,
        -- 异常程度评分
        CASE
            WHEN EXTRACT(HOUR FROM gs.date_ordered) = 0 THEN 10  -- 午夜交易最可疑
            WHEN EXTRACT(HOUR FROM gs.date_ordered) IN (1, 2, 3, 4, 5) THEN 9  -- 深夜交易
            WHEN EXTRACT(DOW FROM gs.date_ordered) IN (0, 6) THEN 7  -- 周末交易
            WHEN EXTRACT(HOUR FROM gs.date_ordered) IN (19, 20, 21, 22, 23) THEN 5  -- 晚间交易
            WHEN EXTRACT(HOUR FROM gs.date_ordered) IN (6, 7) THEN 3  -- 早晨交易
            ELSE 1
        END AS suspicion_score
    FROM glaze_sale gs
    INNER JOIN employee e ON gs.emp_id = e.emp_id
    INNER JOIN job_position jp ON e.job_position_id = jp.job_position_id
    INNER JOIN customer c ON gs.customer_id = c.customer_id
    WHERE (EXTRACT(HOUR FROM gs.date_ordered) NOT BETWEEN 8 AND 18
        OR EXTRACT(DOW FROM gs.date_ordered) IN (0, 6))
        AND gs.sale_amount > 500  -- 过滤小额交易
),
employee_pattern_analysis AS (
    SELECT
        oht.*,
        -- 员工异常模式统计
        COUNT(*) OVER (PARTITION BY employee_name) AS employee_off_hours_count,
        SUM(sale_amount) OVER (PARTITION BY employee_name) AS employee_off_hours_total,
        AVG(suspicion_score) OVER (PARTITION BY employee_name) AS employee_avg_suspicion,
        -- 时间模式分析
        COUNT(*) OVER (PARTITION BY employee_name, time_classification) AS pattern_frequency,
        -- 客户关联分析
        COUNT(DISTINCT customer_name) OVER (PARTITION BY employee_name) AS unique_customers_served
    FROM off_hours_transactions oht
)
SELECT
    'After Hours Transactions' AS fraud_test_type,
    glaze_sale_id AS 交易ID,
    ROUND(sale_amount, 2) AS 交易金额,
    sale_type AS 交易类型,
    date_ordered AS 交易日期时间,
    transaction_hour AS 交易小时,
    CASE
        WHEN day_of_week = 0 THEN '周日'
        WHEN day_of_week = 1 THEN '周一'
        WHEN day_of_week = 2 THEN '周二'
        WHEN day_of_week = 3 THEN '周三'
        WHEN day_of_week = 4 THEN '周四'
        WHEN day_of_week = 5 THEN '周五'
        WHEN day_of_week = 6 THEN '周六'
    END AS 交易星期,
    employee_name AS 处理员工,
    position_title AS 员工职位,
    customer_name AS 客户姓名,
    suburb AS 客户区域,
    time_classification AS 时间分类,
    risk_level AS 风险等级,
    suspicion_score AS 可疑评分,
    employee_off_hours_count AS 员工非工作时间交易数,
    ROUND(employee_off_hours_total, 2) AS 员工非工作时间交易总额,
    ROUND(employee_avg_suspicion, 2) AS 员工平均可疑评分,
    pattern_frequency AS 模式频率,
    unique_customers_served AS 服务客户数量,
    CASE
        WHEN employee_off_hours_count > 10 AND employee_avg_suspicion > 7 THEN '高风险员工-建议立即调查'
        WHEN transaction_hour = 0 AND sale_amount > 20000 THEN '极端可疑-午夜大额交易'
        WHEN time_classification = 'Weekend' AND sale_amount > 15000 THEN '周末大额交易-需要验证'
        WHEN pattern_frequency > 5 THEN '重复模式-可能存在系统性问题'
        ELSE '标准调查程序'
    END AS 调查建议
FROM employee_pattern_analysis
ORDER BY suspicion_score DESC, sale_amount DESC;

-- =====================================================================
-- 欺诈检测5：Caesar密码模式检测 (Roman Emperor Puzzle)
-- 风险类型：隐藏身份和加密通信 (Hidden Identity & Encrypted Communication)
-- 检测目标：基于"Roman Emperor puzzle"提示，检测员工数据中的Caesar密码模式
-- 业务风险：内部串通、身份伪造、隐蔽通信网络
-- 特别关注：Mick Neville和Janie Brightwell的叔侄关系及相关数据异常
-- =====================================================================

-- 查询5：Caesar密码和隐藏模式检测分析
WITH caesar_pattern_analysis AS (
    SELECT
        e.emp_id,
        e.first_name,
        e.last_name,
        e.start_date,
        e.end_date,
        jp.position_title,
        -- Caesar密码数字模式检测（基于数字13的倍数关系）
        CASE
            WHEN e.emp_id % 13 = 0 THEN 'Caesar-13 Pattern'
            WHEN e.emp_id % 26 = 0 THEN 'Caesar-26 Pattern'  -- 字母表长度
            WHEN e.emp_id % 3 = 0 AND e.emp_id % 7 = 0 THEN 'Multiple-Prime Pattern'
            ELSE 'Normal ID'
        END AS id_pattern_type,
        -- 姓名字母位移模式检测
        CASE
            -- 检测连续字母模式
            WHEN first_name ~ '[A-Z]{3,}' OR last_name ~ '[A-Z]{3,}' THEN 'Consecutive Letters'
            -- 检测Caesar位移模式（首字母差值为3的倍数）
            WHEN ABS(ASCII(SUBSTRING(first_name, 1, 1)) - ASCII(SUBSTRING(last_name, 1, 1))) % 3 = 0
                AND ABS(ASCII(SUBSTRING(first_name, 1, 1)) - ASCII(SUBSTRING(last_name, 1, 1))) > 0 THEN 'Caesar Shift Pattern'
            -- 检测回文或对称模式
            WHEN first_name = REVERSE(first_name) OR last_name = REVERSE(last_name) THEN 'Palindrome Pattern'
            -- 检测特殊字母组合
            WHEN first_name LIKE '%ABC%' OR first_name LIKE '%XYZ%'
                OR last_name LIKE '%ABC%' OR last_name LIKE '%XYZ%' THEN 'Sequential Pattern'
            ELSE 'Normal Name'
        END AS name_pattern_type,
        -- 特殊关系检测（重点关注Mick-Janie关系）
        CASE
            WHEN (first_name = 'Mick' AND last_name = 'Neville')
                OR (first_name = 'Janie' AND last_name = 'Brightwell') THEN 'Key Person of Interest'
            WHEN first_name IN ('Mick', 'Michael', 'Mickey')
                OR last_name IN ('Neville', 'Brightwell') THEN 'Related Name Pattern'
            ELSE 'Standard Employee'
        END AS relationship_indicator,
        -- 数字组合分析
        LENGTH(e.emp_id::text) AS id_length,
        e.emp_id % 100 AS id_last_two_digits,
        e.emp_id / 1000 AS id_thousands,
        -- 字母频率分析
        LENGTH(first_name) AS first_name_length,
        LENGTH(last_name) AS last_name_length,
        ASCII(SUBSTRING(first_name, 1, 1)) AS first_name_ascii,
        ASCII(SUBSTRING(last_name, 1, 1)) AS last_name_ascii
    FROM employee e
    INNER JOIN job_position jp ON e.job_position_id = jp.job_position_id
),
suspicious_patterns AS (
    SELECT
        cpa.*,
        -- 综合可疑评分计算
        (CASE WHEN id_pattern_type != 'Normal ID' THEN 3 ELSE 0 END +
         CASE WHEN name_pattern_type != 'Normal Name' THEN 4 ELSE 0 END +
         CASE WHEN relationship_indicator = 'Key Person of Interest' THEN 8 ELSE 0 END +
         CASE WHEN relationship_indicator = 'Related Name Pattern' THEN 2 ELSE 0 END +
         CASE WHEN id_last_two_digits IN (13, 26, 39, 52, 65, 78, 91) THEN 2 ELSE 0 END +
         CASE WHEN ABS(first_name_ascii - last_name_ascii) = 13 THEN 5 ELSE 0 END) AS total_suspicion_score,
        -- 交易关联分析
        (SELECT COUNT(*) FROM glaze_sale gs WHERE gs.emp_id = cpa.emp_id) AS total_transactions,
        (SELECT SUM(sale_amount) FROM glaze_sale gs WHERE gs.emp_id = cpa.emp_id) AS total_transaction_amount,
        -- 权限异常检测
        (SELECT COUNT(*) FROM authorizations a WHERE a.emp_id = cpa.emp_id) AS authorization_count,
        -- 退休员工特殊检测
        CASE
            WHEN end_date IS NOT NULL AND total_transactions > 0 THEN 'Retired with Active Transactions'
            WHEN end_date IS NOT NULL THEN 'Retired Employee'
            ELSE 'Active Employee'
        END AS employment_status
    FROM caesar_pattern_analysis cpa
),
network_analysis AS (
    SELECT
        sp.*,
        -- 关联网络分析（查找可能的共犯关系）
        (SELECT COUNT(DISTINCT gs2.emp_id)
         FROM glaze_sale gs1
         INNER JOIN glaze_sale gs2 ON gs1.customer_id = gs2.customer_id
         WHERE gs1.emp_id = sp.emp_id AND gs2.emp_id != sp.emp_id) AS connected_employees,
        -- 数据中心访问权限检测（基于案例背景）
        CASE
            WHEN position_title IN ('DBA', 'IT Manager', 'Receptionist') THEN 'Data Center Access'
            WHEN first_name = 'Mick' AND last_name = 'Neville' THEN 'Unauthorized Access Risk'
            ELSE 'Standard Access'
        END AS access_risk_level
    FROM suspicious_patterns sp
)
SELECT
    'Caesar Cipher Detection' AS fraud_test_type,
    emp_id AS 员工ID,
    first_name AS 名字,
    last_name AS 姓氏,
    position_title AS 职位,
    id_pattern_type AS ID模式类型,
    name_pattern_type AS 姓名模式类型,
    relationship_indicator AS 关系指标,
    total_suspicion_score AS 总可疑评分,
    employment_status AS 就业状态,
    access_risk_level AS 访问风险等级,
    total_transactions AS 总交易数,
    ROUND(COALESCE(total_transaction_amount, 0), 2) AS 总交易金额,
    authorization_count AS 权限记录数,
    connected_employees AS 关联员工数,
    id_last_two_digits AS ID末两位,
    ABS(first_name_ascii - last_name_ascii) AS 姓名ASCII差值,
    CASE
        WHEN total_suspicion_score >= 15 THEN '极高风险-立即深入调查'
        WHEN total_suspicion_score >= 10 THEN '高风险-优先调查'
        WHEN total_suspicion_score >= 5 THEN '中等风险-定期监控'
        WHEN total_suspicion_score > 0 THEN '低风险-基础关注'
        ELSE '正常员工'
    END AS 风险等级建议,
    CASE
        WHEN first_name = 'Mick' AND last_name = 'Neville' THEN
            '重点关注：退休员工数据中心访问，与Janie Brightwell叔侄关系'
        WHEN first_name = 'Janie' AND last_name = 'Brightwell' THEN
            '重点关注：前台权限，薪资报告准备，与Mick Neville叔侄关系'
        WHEN id_pattern_type != 'Normal ID' AND name_pattern_type != 'Normal Name' THEN
            '双重模式匹配：建议验证身份真实性'
        WHEN total_suspicion_score >= 10 THEN
            '多重风险指标：建议全面背景调查'
        ELSE '标准监控程序'
    END AS 具体调查建议,
    start_date AS 入职日期,
    end_date AS 离职日期
FROM network_analysis
WHERE total_suspicion_score > 0  -- 只显示有可疑模式的员工
ORDER BY total_suspicion_score DESC, total_transaction_amount DESC;

-- =====================================================================
-- 欺诈检测6：员工关系网络异常检测
-- 风险类型：内部串通和关系网络欺诈 (Collusion & Relationship Network Fraud)
-- 检测目标：识别异常的员工关系网络和可疑的协作模式
-- 业务风险：内部串通、权限滥用、家族关系利用
-- 特别关注：Mick Neville和Janie Brightwell的叔侄关系及其对业务的影响
-- =====================================================================

-- 查询6：员工关系网络和串通风险检测
WITH employee_relationship_network AS (
    SELECT
        e1.emp_id AS employee_1_id,
        e1.first_name || ' ' || e1.last_name AS employee_1_name,
        jp1.position_title AS employee_1_position,
        e2.emp_id AS employee_2_id,
        e2.first_name || ' ' || e2.last_name AS employee_2_name,
        jp2.position_title AS employee_2_position,
        -- 关系类型识别
        CASE
            WHEN (e1.first_name = 'Mick' AND e1.last_name = 'Neville' AND
                  e2.first_name = 'Janie' AND e2.last_name = 'Brightwell') OR
                 (e1.first_name = 'Janie' AND e1.last_name = 'Brightwell' AND
                  e2.first_name = 'Mick' AND e2.last_name = 'Neville') THEN 'Known Family Relationship'
            WHEN e1.last_name = e2.last_name AND e1.emp_id != e2.emp_id THEN 'Potential Family Relationship'
            WHEN ABS(e1.emp_id - e2.emp_id) <= 5 THEN 'Sequential Employee IDs'
            WHEN e1.start_date = e2.start_date THEN 'Same Start Date'
            ELSE 'Standard Relationship'
        END AS relationship_type,
        -- 共同客户分析
        COUNT(DISTINCT gs1.customer_id) AS shared_customers,
        -- 交易重叠分析
        COUNT(CASE WHEN gs1.customer_id = gs2.customer_id
                   AND ABS(EXTRACT(DAYS FROM (gs1.date_ordered - gs2.date_ordered))) <= 7
                   THEN 1 END) AS overlapping_transactions,
        -- 金额相关性分析
        SUM(gs1.sale_amount) AS employee_1_total_amount,
        SUM(gs2.sale_amount) AS employee_2_total_amount,
        AVG(gs1.sale_amount) AS employee_1_avg_amount,
        AVG(gs2.sale_amount) AS employee_2_avg_amount,
        -- 时间模式分析
        COUNT(CASE WHEN EXTRACT(HOUR FROM gs1.date_ordered) = EXTRACT(HOUR FROM gs2.date_ordered)
                   AND gs1.customer_id = gs2.customer_id THEN 1 END) AS same_hour_transactions
    FROM employee e1
    INNER JOIN job_position jp1 ON e1.job_position_id = jp1.job_position_id
    CROSS JOIN employee e2
    INNER JOIN job_position jp2 ON e2.job_position_id = jp2.job_position_id
    LEFT JOIN glaze_sale gs1 ON e1.emp_id = gs1.emp_id
    LEFT JOIN glaze_sale gs2 ON e2.emp_id = gs2.emp_id
    WHERE e1.emp_id < e2.emp_id  -- 避免重复配对
        AND (e1.end_date IS NULL OR e2.end_date IS NULL)  -- 至少一人在职
    GROUP BY e1.emp_id, e1.first_name, e1.last_name, jp1.position_title,
             e2.emp_id, e2.first_name, e2.last_name, jp2.position_title
),
risk_assessment AS (
    SELECT
        ern.*,
        -- 风险评分计算
        (CASE WHEN relationship_type = 'Known Family Relationship' THEN 10 ELSE 0 END +
         CASE WHEN relationship_type = 'Potential Family Relationship' THEN 5 ELSE 0 END +
         CASE WHEN shared_customers > 10 THEN 4 ELSE 0 END +
         CASE WHEN overlapping_transactions > 5 THEN 3 ELSE 0 END +
         CASE WHEN same_hour_transactions > 3 THEN 3 ELSE 0 END +
         CASE WHEN ABS(employee_1_avg_amount - employee_2_avg_amount) < 100 THEN 2 ELSE 0 END) AS collusion_risk_score,
        -- 权限冲突检测
        CASE
            WHEN (employee_1_position IN ('DBA', 'IT Manager') AND employee_2_position = 'Receptionist') OR
                 (employee_2_position IN ('DBA', 'IT Manager') AND employee_1_position = 'Receptionist') THEN 'High Authority Conflict'
            WHEN employee_1_position = employee_2_position THEN 'Same Role Potential Conflict'
            ELSE 'Standard Authority Level'
        END AS authority_conflict_level,
        -- 业务影响评估
        CASE
            WHEN shared_customers > 20 AND overlapping_transactions > 10 THEN 'High Business Impact'
            WHEN shared_customers > 10 OR overlapping_transactions > 5 THEN 'Medium Business Impact'
            WHEN shared_customers > 0 THEN 'Low Business Impact'
            ELSE 'No Direct Impact'
        END AS business_impact_level
    FROM employee_relationship_network ern
    WHERE shared_customers > 0 OR relationship_type != 'Standard Relationship'
),
authorization_cross_check AS (
    SELECT
        ra.*,
        -- 权限交叉检查
        (SELECT COUNT(*) FROM authorizations a1 WHERE a1.emp_id = ra.employee_1_id) AS employee_1_authorizations,
        (SELECT COUNT(*) FROM authorizations a2 WHERE a2.emp_id = ra.employee_2_id) AS employee_2_authorizations,
        -- 共同权限检测
        (SELECT COUNT(*) FROM authorizations a1
         INNER JOIN authorizations a2 ON a1.table_name = a2.table_name
         WHERE a1.emp_id = ra.employee_1_id AND a2.emp_id = ra.employee_2_id) AS shared_authorizations,
        -- 特殊关注案例标记
        CASE
            WHEN (employee_1_name = 'Mick Neville' AND employee_2_name = 'Janie Brightwell') OR
                 (employee_1_name = 'Janie Brightwell' AND employee_2_name = 'Mick Neville') THEN
                'Critical Case: Uncle-Niece Relationship with Data Center Access'
            ELSE 'Standard Case'
        END AS special_attention_flag
    FROM risk_assessment ra
)
SELECT
    'Employee Network Anomalies' AS fraud_test_type,
    employee_1_id AS 员工1_ID,
    employee_1_name AS 员工1_姓名,
    employee_1_position AS 员工1_职位,
    employee_2_id AS 员工2_ID,
    employee_2_name AS 员工2_姓名,
    employee_2_position AS 员工2_职位,
    relationship_type AS 关系类型,
    shared_customers AS 共同客户数,
    overlapping_transactions AS 重叠交易数,
    same_hour_transactions AS 同时段交易数,
    collusion_risk_score AS 串通风险评分,
    authority_conflict_level AS 权限冲突等级,
    business_impact_level AS 业务影响等级,
    employee_1_authorizations AS 员工1权限数,
    employee_2_authorizations AS 员工2权限数,
    shared_authorizations AS 共同权限数,
    ROUND(employee_1_total_amount, 2) AS 员工1总交易额,
    ROUND(employee_2_total_amount, 2) AS 员工2总交易额,
    ROUND(employee_1_avg_amount, 2) AS 员工1平均交易额,
    ROUND(employee_2_avg_amount, 2) AS 员工2平均交易额,
    special_attention_flag AS 特别关注标记,
    CASE
        WHEN collusion_risk_score >= 15 THEN '极高风险-立即隔离调查'
        WHEN collusion_risk_score >= 10 THEN '高风险-深入调查'
        WHEN collusion_risk_score >= 5 THEN '中等风险-加强监控'
        ELSE '低风险-常规监控'
    END AS 风险等级,
    CASE
        WHEN special_attention_flag LIKE 'Critical Case%' THEN
            '立即行动：审查数据中心访问日志，验证薪资报告准备过程，检查AI提示使用记录'
        WHEN authority_conflict_level = 'High Authority Conflict' AND shared_authorizations > 0 THEN
            '权限审查：检查权限使用日志，验证业务合理性'
        WHEN overlapping_transactions > 10 THEN
            '交易审查：验证重叠交易的业务合理性和客户真实性'
        WHEN collusion_risk_score >= 10 THEN
            '全面调查：背景调查、通信记录、财务关系'
        ELSE '标准监控程序'
    END AS 调查建议
FROM authorization_cross_check
WHERE collusion_risk_score > 0 OR special_attention_flag != 'Standard Case'
ORDER BY collusion_risk_score DESC, shared_customers DESC;

-- =====================================================================
-- Q4部分查询总结
-- =====================================================================
-- 本SQL文件包含6个核心欺诈检测查询，基于欺诈三角理论进行全面风险评估：
-- 1. 自我批准违规检测 - 识别职责分离违规和内控缺陷
-- 2. 重复付款检测 - 发现供应商欺诈和系统漏洞
-- 3. 异常高额交易检测 - 使用统计分析识别异常交易模式
-- 4. 工作时间外交易检测 - 识别时间异常和未授权访问
-- 5. Caesar密码模式检测 - 基于"Roman Emperor puzzle"的隐藏模式识别
-- 6. 员工关系网络异常检测 - 发现内部串通和关系网络风险
--
-- 特别关注GigaGlow公司的关键风险点：
-- - Mick Neville和Janie Brightwell的叔侄关系及数据中心访问权限
-- - 现金流紧张环境下的财务压力驱动欺诈风险
-- - 老旧IT系统的安全漏洞和权限控制缺陷
-- - CEO关注的盈利困难与销售强劲的矛盾现象
--
-- 所有查询均基于PostgreSQL语法，使用了高级分析技术包括：
-- - 统计异常检测（Z-score分析）
-- - 模式识别算法（Caesar密码检测）
-- - 网络关系分析（员工关联检测）
-- - 时间序列分析（异常时间模式）
-- - 风险评分模型（多维度风险量化）
-- =====================================================================
