-- =====================================================================
-- GigaGlow公司运营效能分析SQL查询集
-- PostgreSQL数据库课程作业 - Q3部分
-- 作者：数据库分析团队
-- 创建日期：2024年
-- =====================================================================

-- =====================================================================
-- 问题1：St Lucia地区2022-2025年购买电池的客户数量统计
-- 业务背景：CEO Jasmine Rivers关注St Lucia地区的电池业务发展情况
-- 分析目标：统计该地区电池销售的客户数量和销售趋势
-- =====================================================================

-- 查询1：St Lucia地区电池客户统计分析
WITH st_lucia_battery_sales AS (
    SELECT 
        gs.customer_id,
        c.customer_name,
        c.suburb,
        gs.sale_amount,
        gs.date_ordered,
        EXTRACT(YEAR FROM gs.date_ordered) AS sale_year,
        -- 电池类型分析（基于价格识别）
        CASE 
            WHEN gs.sale_amount = 11000 THEN '20kWh电池系统'
            WHEN gs.sale_amount = 16500 THEN '30kWh电池系统'
            WHEN gs.sale_amount = 22000 THEN '40kWh电池系统'
            ELSE '其他电池产品'
        END AS battery_type,
        -- 客户价值分类
        CASE 
            WHEN gs.sale_amount >= 20000 THEN '高价值客户'
            WHEN gs.sale_amount >= 15000 THEN '中等价值客户'
            ELSE '标准客户'
        END AS customer_value_tier
    FROM glaze_sale gs
    INNER JOIN customer c ON gs.customer_id = c.customer_id
    WHERE c.suburb = 'ST LUCIA'
        AND gs.sale_type = 'BATTERY'
        AND EXTRACT(YEAR FROM gs.date_ordered) BETWEEN 2022 AND 2025
        AND gs.sale_amount > 0
),
yearly_summary AS (
    SELECT 
        sale_year,
        COUNT(DISTINCT customer_id) AS unique_customers,
        COUNT(*) AS total_battery_sales,
        SUM(sale_amount) AS total_revenue,
        AVG(sale_amount) AS avg_sale_amount,
        -- 电池类型分布统计
        COUNT(CASE WHEN battery_type = '20kWh电池系统' THEN 1 END) AS battery_20kwh_count,
        COUNT(CASE WHEN battery_type = '30kWh电池系统' THEN 1 END) AS battery_30kwh_count,
        COUNT(CASE WHEN battery_type = '40kWh电池系统' THEN 1 END) AS battery_40kwh_count,
        -- 客户价值分布
        COUNT(CASE WHEN customer_value_tier = '高价值客户' THEN 1 END) AS high_value_customers,
        COUNT(CASE WHEN customer_value_tier = '中等价值客户' THEN 1 END) AS medium_value_customers,
        COUNT(CASE WHEN customer_value_tier = '标准客户' THEN 1 END) AS standard_customers
    FROM st_lucia_battery_sales
    GROUP BY sale_year
)
SELECT 
    sale_year AS 年份,
    unique_customers AS 购买电池客户数量,
    total_battery_sales AS 电池销售总笔数,
    ROUND(total_revenue, 2) AS 总收入,
    ROUND(avg_sale_amount, 2) AS 平均销售金额,
    battery_20kwh_count AS "20kWh电池销量",
    battery_30kwh_count AS "30kWh电池销量", 
    battery_40kwh_count AS "40kWh电池销量",
    high_value_customers AS 高价值客户数,
    medium_value_customers AS 中等价值客户数,
    standard_customers AS 标准客户数,
    -- 年度增长率计算
    ROUND(
        (unique_customers - LAG(unique_customers) OVER (ORDER BY sale_year)) * 100.0 / 
        NULLIF(LAG(unique_customers) OVER (ORDER BY sale_year), 0), 2
    ) AS 客户数量年增长率百分比
FROM yearly_summary
ORDER BY sale_year;

-- =====================================================================
-- 问题2：St Lucia地区屋顶清洁承包商数量和实际服务情况分析
-- 业务背景：CEO提到"太多时间浪费在发现屋顶未正确准备的房屋访问上"
-- 分析目标：评估St Lucia地区承包商网络的覆盖情况和服务质量
-- =====================================================================

-- 查询2：St Lucia地区承包商网络分析
WITH st_lucia_cleaning_services AS (
    SELECT 
        gs.emp_id AS contractor_id,
        e.first_name || ' ' || e.last_name AS contractor_name,
        jp.position_title,
        gs.customer_id,
        c.customer_name,
        c.suburb,
        gs.sale_amount AS cleaning_fee,
        gs.date_ordered AS service_date,
        -- 服务频率分析
        COUNT(*) OVER (PARTITION BY gs.emp_id) AS total_services_by_contractor,
        COUNT(*) OVER (PARTITION BY gs.customer_id) AS services_per_customer,
        -- 月度服务统计
        EXTRACT(YEAR FROM gs.date_ordered) AS service_year,
        EXTRACT(MONTH FROM gs.date_ordered) AS service_month
    FROM glaze_sale gs
    INNER JOIN customer c ON gs.customer_id = c.customer_id
    INNER JOIN employee e ON gs.emp_id = e.emp_id
    INNER JOIN job_position jp ON e.job_position_id = jp.job_position_id
    WHERE c.suburb = 'ST LUCIA'
        AND gs.sale_type = 'CLEANING-FEE'
        AND gs.sale_amount > 0
        AND e.end_date IS NULL  -- 仅统计在职承包商
),
contractor_performance AS (
    SELECT 
        contractor_id,
        contractor_name,
        position_title,
        COUNT(DISTINCT customer_id) AS unique_customers_served,
        COUNT(*) AS total_cleaning_services,
        SUM(cleaning_fee) AS total_cleaning_revenue,
        AVG(cleaning_fee) AS avg_cleaning_fee,
        MIN(service_date) AS first_service_date,
        MAX(service_date) AS latest_service_date,
        -- 服务活跃度评估
        EXTRACT(DAYS FROM (MAX(service_date) - MIN(service_date))) AS service_period_days,
        ROUND(COUNT(*)::DECIMAL / NULLIF(EXTRACT(DAYS FROM (MAX(service_date) - MIN(service_date))), 0) * 30, 2) AS avg_services_per_month,
        -- 客户满意度关联分析（通过后续涂装转化率评估）
        COUNT(DISTINCT CASE 
            WHEN EXISTS (
                SELECT 1 FROM glaze_sale gs2 
                WHERE gs2.customer_id = sls.customer_id 
                AND gs2.sale_type = 'GLAZE'
                AND gs2.date_ordered > sls.service_date
            ) THEN sls.customer_id 
        END) AS customers_converted_to_glaze
    FROM st_lucia_cleaning_services sls
    GROUP BY contractor_id, contractor_name, position_title
),
service_quality_metrics AS (
    SELECT 
        cp.*,
        -- 转化率计算（清洁到涂装的转化率）
        ROUND(
            customers_converted_to_glaze * 100.0 / NULLIF(unique_customers_served, 0), 2
        ) AS cleaning_to_glaze_conversion_rate,
        -- 承包商效率评级
        CASE 
            WHEN avg_services_per_month >= 10 THEN '高效承包商'
            WHEN avg_services_per_month >= 5 THEN '标准承包商'
            WHEN avg_services_per_month >= 2 THEN '低效承包商'
            ELSE '不活跃承包商'
        END AS contractor_efficiency_rating,
        -- 服务质量评级（基于转化率）
        CASE 
            WHEN customers_converted_to_glaze * 100.0 / NULLIF(unique_customers_served, 0) >= 40 THEN '优质服务'
            WHEN customers_converted_to_glaze * 100.0 / NULLIF(unique_customers_served, 0) >= 25 THEN '标准服务'
            WHEN customers_converted_to_glaze * 100.0 / NULLIF(unique_customers_served, 0) >= 10 THEN '待改进服务'
            ELSE '低质量服务'
        END AS service_quality_rating
    FROM contractor_performance cp
)
SELECT 
    contractor_id AS 承包商ID,
    contractor_name AS 承包商姓名,
    position_title AS 职位,
    unique_customers_served AS 服务客户数量,
    total_cleaning_services AS 清洁服务总次数,
    ROUND(total_cleaning_revenue, 2) AS 清洁服务总收入,
    ROUND(avg_cleaning_fee, 2) AS 平均清洁费用,
    customers_converted_to_glaze AS 转化为涂装客户数,
    cleaning_to_glaze_conversion_rate AS 清洁到涂装转化率百分比,
    ROUND(avg_services_per_month, 2) AS 月均服务次数,
    contractor_efficiency_rating AS 承包商效率评级,
    service_quality_rating AS 服务质量评级,
    service_period_days AS 服务周期天数,
    first_service_date AS 首次服务日期,
    latest_service_date AS 最近服务日期
FROM service_quality_metrics
ORDER BY total_cleaning_services DESC, cleaning_to_glaze_conversion_rate DESC;

-- =====================================================================
-- 问题3：在职电气安装工列表及安装数量排名（按降序排列）
-- 业务背景：评估电气安装工的工作绩效和生产力
-- 分析目标：识别高绩效员工和绩效改进机会
-- =====================================================================

-- 查询3：电气安装工绩效分析
WITH electrical_installer_performance AS (
    SELECT 
        e.emp_id,
        e.first_name || ' ' || e.last_name AS installer_name,
        e.start_date,
        -- 计算工作经验
        EXTRACT(DAYS FROM (CURRENT_DATE - e.start_date)) / 365.0 AS years_of_experience,
        -- 逆变器安装统计
        COUNT(CASE WHEN gs.sale_type = 'INVERTER' THEN 1 END) AS inverter_installations,
        SUM(CASE WHEN gs.sale_type = 'INVERTER' THEN gs.sale_amount ELSE 0 END) AS total_inverter_revenue,
        AVG(CASE WHEN gs.sale_type = 'INVERTER' THEN gs.sale_amount END) AS avg_inverter_value,
        -- 逆变器类型分析（基于标准价格）
        COUNT(CASE WHEN gs.sale_type = 'INVERTER' AND gs.sale_amount = 1650 THEN 1 END) AS inverter_10kw_count,
        COUNT(CASE WHEN gs.sale_type = 'INVERTER' AND gs.sale_amount = 2145 THEN 1 END) AS inverter_15kw_count,
        COUNT(CASE WHEN gs.sale_type = 'INVERTER' AND gs.sale_amount = 2530 THEN 1 END) AS inverter_20kw_count,
        -- 服务时间分析
        MIN(gs.date_ordered) AS first_installation_date,
        MAX(gs.date_ordered) AS latest_installation_date,
        -- 客户服务范围
        COUNT(DISTINCT gs.customer_id) AS unique_customers_served,
        COUNT(DISTINCT c.suburb) AS service_areas_covered
    FROM employee e
    INNER JOIN job_position jp ON e.job_position_id = jp.job_position_id
    LEFT JOIN glaze_sale gs ON e.emp_id = gs.emp_id AND gs.sale_type = 'INVERTER'
    LEFT JOIN customer c ON gs.customer_id = c.customer_id
    WHERE jp.position_title = 'Electrical Installer'
        AND e.end_date IS NULL  -- 仅统计在职员工
    GROUP BY e.emp_id, e.first_name, e.last_name, e.start_date
),
performance_metrics AS (
    SELECT 
        *,
        -- 绩效指标计算
        ROUND(inverter_installations / NULLIF(years_of_experience, 0), 2) AS installations_per_year,
        ROUND(total_inverter_revenue / NULLIF(years_of_experience, 0), 2) AS revenue_per_year,
        ROUND(total_inverter_revenue / NULLIF(inverter_installations, 0), 2) AS revenue_per_installation,
        -- 绩效评级
        CASE 
            WHEN inverter_installations >= 50 THEN '顶级绩效'
            WHEN inverter_installations >= 30 THEN '优秀绩效'
            WHEN inverter_installations >= 15 THEN '良好绩效'
            WHEN inverter_installations >= 5 THEN '标准绩效'
            ELSE '待提升绩效'
        END AS performance_rating,
        -- 专业化程度评估
        CASE 
            WHEN inverter_20kw_count * 100.0 / NULLIF(inverter_installations, 0) >= 60 THEN '高端专业化'
            WHEN inverter_15kw_count * 100.0 / NULLIF(inverter_installations, 0) >= 50 THEN '中端专业化'
            WHEN inverter_10kw_count * 100.0 / NULLIF(inverter_installations, 0) >= 60 THEN '入门级专业化'
            ELSE '全能型技师'
        END AS specialization_type
    FROM electrical_installer_performance
)
SELECT 
    ROW_NUMBER() OVER (ORDER BY inverter_installations DESC) AS 绩效排名,
    emp_id AS 员工ID,
    installer_name AS 安装工姓名,
    inverter_installations AS 逆变器安装数量,
    ROUND(total_inverter_revenue, 2) AS 逆变器安装总收入,
    ROUND(avg_inverter_value, 2) AS 平均安装价值,
    inverter_10kw_count AS "10kW逆变器安装数",
    inverter_15kw_count AS "15kW逆变器安装数", 
    inverter_20kw_count AS "20kW逆变器安装数",
    unique_customers_served AS 服务客户数量,
    service_areas_covered AS 服务区域数量,
    ROUND(years_of_experience, 1) AS 工作经验年数,
    ROUND(installations_per_year, 2) AS 年均安装数量,
    ROUND(revenue_per_year, 2) AS 年均创收,
    performance_rating AS 绩效评级,
    specialization_type AS 专业化类型,
    start_date AS 入职日期,
    first_installation_date AS 首次安装日期,
    latest_installation_date AS 最近安装日期
FROM performance_metrics
ORDER BY inverter_installations DESC, total_inverter_revenue DESC;

-- =====================================================================
-- 问题4：按净值排序的前10个高价值服务区域分析
-- 业务背景：识别最有价值的服务区域，优化资源配置
-- 分析目标：计算各区域的净收入、客户密度和市场潜力
-- =====================================================================

-- 查询4：高价值服务区域分析
WITH regional_business_metrics AS (
    SELECT
        c.suburb AS service_area,
        -- 客户和服务统计
        COUNT(DISTINCT gs.customer_id) AS customer_count,
        COUNT(gs.glaze_sale_id) AS total_services,
        COUNT(DISTINCT gs.sale_type) AS service_types_offered,
        -- 收入分析
        SUM(gs.sale_amount) AS total_revenue,
        AVG(gs.sale_amount) AS avg_transaction_value,
        -- 服务类型收入分解
        SUM(CASE WHEN gs.sale_type = 'GLAZE' THEN gs.sale_amount ELSE 0 END) AS glaze_revenue,
        SUM(CASE WHEN gs.sale_type = 'INVERTER' THEN gs.sale_amount ELSE 0 END) AS inverter_revenue,
        SUM(CASE WHEN gs.sale_type = 'BATTERY' THEN gs.sale_amount ELSE 0 END) AS battery_revenue,
        SUM(CASE WHEN gs.sale_type = 'CLEANING-FEE' THEN gs.sale_amount ELSE 0 END) AS cleaning_revenue,
        SUM(CASE WHEN gs.sale_type = 'MATCHING-FEE' THEN gs.sale_amount ELSE 0 END) AS matching_revenue,
        -- 成本估算（基于业务模式）
        SUM(CASE WHEN gs.sale_type = 'CLEANING-FEE' THEN gs.sale_amount * 0.9 ELSE 0 END) AS cleaning_costs,
        SUM(CASE WHEN gs.sale_type = 'GLAZE' THEN gs.sale_amount * 0.6 ELSE 0 END) AS glaze_costs,
        SUM(CASE WHEN gs.sale_type = 'INVERTER' THEN gs.sale_amount * 0.7 ELSE 0 END) AS inverter_costs,
        SUM(CASE WHEN gs.sale_type = 'BATTERY' THEN gs.sale_amount * 0.75 ELSE 0 END) AS battery_costs,
        -- 时间分析
        MIN(gs.date_ordered) AS first_service_date,
        MAX(gs.date_ordered) AS latest_service_date,
        -- 客户活跃度
        COUNT(CASE WHEN gs.date_ordered >= CURRENT_DATE - INTERVAL '6 months' THEN 1 END) AS recent_services
    FROM glaze_sale gs
    INNER JOIN customer c ON gs.customer_id = c.customer_id
    WHERE gs.sale_amount > 0
    GROUP BY c.suburb
),
profitability_analysis AS (
    SELECT
        service_area,
        customer_count,
        total_services,
        service_types_offered,
        ROUND(total_revenue, 2) AS total_revenue,
        -- 净值计算（收入减去估算成本）
        ROUND(total_revenue - (cleaning_costs + glaze_costs + inverter_costs + battery_costs), 2) AS net_value,
        ROUND((total_revenue - (cleaning_costs + glaze_costs + inverter_costs + battery_costs)) / NULLIF(total_revenue, 0) * 100, 2) AS profit_margin_percentage,
        -- 效率指标
        ROUND(total_revenue / NULLIF(customer_count, 0), 2) AS revenue_per_customer,
        ROUND(total_services::DECIMAL / NULLIF(customer_count, 0), 2) AS services_per_customer,
        ROUND(avg_transaction_value, 2) AS avg_transaction_value,
        -- 收入结构分析
        ROUND(glaze_revenue, 2) AS glaze_revenue,
        ROUND(inverter_revenue, 2) AS inverter_revenue,
        ROUND(battery_revenue, 2) AS battery_revenue,
        ROUND(cleaning_revenue, 2) AS cleaning_revenue,
        ROUND(matching_revenue, 2) AS matching_revenue,
        -- 市场活跃度
        ROUND(recent_services * 100.0 / NULLIF(total_services, 0), 2) AS recent_activity_rate,
        EXTRACT(DAYS FROM latest_service_date - first_service_date) AS market_tenure_days,
        -- 市场成熟度评估
        CASE
            WHEN service_types_offered >= 8 THEN 'Mature Market'
            WHEN service_types_offered >= 5 THEN 'Developing Market'
            WHEN service_types_offered >= 3 THEN 'Emerging Market'
            ELSE 'Limited Market'
        END AS market_maturity_level
    FROM regional_business_metrics
    WHERE customer_count >= 3  -- 过滤小规模市场
),
competitive_ranking AS (
    SELECT *,
        -- 多维度排名
        RANK() OVER(ORDER BY net_value DESC) AS net_value_rank,
        RANK() OVER(ORDER BY revenue_per_customer DESC) AS efficiency_rank,
        RANK() OVER(ORDER BY service_types_offered DESC) AS diversity_rank,
        RANK() OVER(ORDER BY recent_activity_rate DESC) AS activity_rank,
        -- 综合竞争力评分
        ROUND(
            (RANK() OVER(ORDER BY net_value DESC) * 0.4 +
             RANK() OVER(ORDER BY revenue_per_customer DESC) * 0.3 +
             RANK() OVER(ORDER BY service_types_offered DESC) * 0.2 +
             RANK() OVER(ORDER BY recent_activity_rate DESC) * 0.1), 2
        ) AS overall_competitiveness_score
    FROM profitability_analysis
)
SELECT
    net_value_rank AS 净值排名,
    service_area AS 服务区域,
    customer_count AS 客户数量,
    total_services AS 服务总数,
    net_value AS 净值,
    revenue_per_customer AS 人均收入,
    services_per_customer AS 人均服务数,
    service_types_offered AS 服务类型数量,
    recent_activity_rate AS 近期活跃度百分比,
    market_maturity_level AS 市场成熟度,
    efficiency_rank AS 效率排名,
    diversity_rank AS 多样性排名,
    overall_competitiveness_score AS 综合竞争力评分,
    CASE
        WHEN net_value_rank <= 3 THEN 'Strategic Priority Market'
        WHEN net_value_rank <= 7 THEN 'High Value Market'
        ELSE 'Standard Market'
    END AS strategic_classification,
    glaze_revenue AS 涂装收入,
    inverter_revenue AS 逆变器收入,
    battery_revenue AS 电池收入,
    cleaning_revenue AS 清洁收入,
    matching_revenue AS 匹配收入,
    profit_margin_percentage AS 利润率百分比
FROM competitive_ranking
ORDER BY net_value DESC
LIMIT 10;

-- =====================================================================
-- 问题5：各职位员工数量统计（不含离职员工，按降序排列）
-- 业务背景：了解GigaGlow的人力资源结构和组织架构
-- 分析目标：分析员工分布、薪资结构和生产力贡献
-- =====================================================================

-- 查询5：人力资源结构与生产力分析
WITH workforce_structure AS (
    SELECT
        jp.position_title AS job_title,
        COUNT(*) AS employee_count,
        COUNT(CASE WHEN e.end_date IS NULL THEN 1 END) AS active_employees,
        COUNT(CASE WHEN e.end_date IS NOT NULL THEN 1 END) AS former_employees,
        -- 薪资统计（使用payroll_detail表的total_payment字段）
        AVG(CASE WHEN e.end_date IS NULL THEN pd.total_payment END) AS avg_current_payment,
        MIN(CASE WHEN e.end_date IS NULL THEN e.start_date END) AS earliest_hire_date,
        MAX(CASE WHEN e.end_date IS NULL THEN e.start_date END) AS latest_hire_date,
        -- 工作经验分析
        AVG(CASE
            WHEN e.end_date IS NULL THEN EXTRACT(DAYS FROM (CURRENT_DATE - e.start_date)) / 365.0
            ELSE NULL
        END) AS avg_tenure_years
    FROM job_position jp
    LEFT JOIN employee e ON jp.job_position_id = e.job_position_id
    LEFT JOIN payroll_detail pd ON e.emp_id = pd.emp_id
    GROUP BY jp.position_title
),
productivity_metrics AS (
    SELECT
        ws.*,
        -- 业务贡献分析
        COUNT(gs.glaze_sale_id) AS total_transactions,
        SUM(gs.sale_amount) AS total_revenue_contribution,
        AVG(gs.sale_amount) AS avg_transaction_value,
        COUNT(DISTINCT gs.customer_id) AS unique_customers_served,
        -- 效率指标
        CASE
            WHEN ws.active_employees > 0 THEN ROUND(COUNT(gs.glaze_sale_id)::DECIMAL / ws.active_employees, 2)
            ELSE 0
        END AS transactions_per_employee,
        CASE
            WHEN ws.active_employees > 0 THEN ROUND(SUM(gs.sale_amount) / ws.active_employees, 2)
            ELSE 0
        END AS revenue_per_employee
    FROM workforce_structure ws
    LEFT JOIN employee e ON e.job_position_id = (
        SELECT job_position_id FROM job_position WHERE position_title = ws.job_title
    )
    LEFT JOIN glaze_sale gs ON e.emp_id = gs.emp_id
    WHERE ws.active_employees > 0  -- 只显示有在职员工的职位
    GROUP BY ws.job_title, ws.employee_count, ws.active_employees, ws.former_employees,
             ws.avg_current_payment, ws.earliest_hire_date, ws.latest_hire_date, ws.avg_tenure_years
),
strategic_analysis AS (
    SELECT *,
        -- 人力资源战略分类
        CASE
            WHEN revenue_per_employee > 50000 THEN 'High Value Position'
            WHEN revenue_per_employee > 20000 THEN 'Medium Value Position'
            WHEN revenue_per_employee > 0 THEN 'Support Position'
            ELSE 'Administrative Position'
        END AS strategic_value_category,
        -- 稳定性评估
        CASE
            WHEN avg_tenure_years > 3 THEN 'Stable Workforce'
            WHEN avg_tenure_years > 1 THEN 'Moderate Stability'
            ELSE 'High Turnover Risk'
        END AS workforce_stability,
        -- 排名
        RANK() OVER(ORDER BY active_employees DESC) AS headcount_rank,
        RANK() OVER(ORDER BY revenue_per_employee DESC) AS productivity_rank
    FROM productivity_metrics
)
SELECT
    headcount_rank AS 员工数量排名,
    job_title AS 职位名称,
    active_employees AS 在职员工数量,
    ROUND(avg_current_payment, 0) AS 平均薪资,
    total_transactions AS 总交易数,
    ROUND(total_revenue_contribution, 2) AS 总收入贡献,
    ROUND(revenue_per_employee, 2) AS 人均创收,
    ROUND(transactions_per_employee, 2) AS 人均交易数,
    ROUND(avg_tenure_years, 1) AS 平均工龄年数,
    strategic_value_category AS 战略价值分类,
    workforce_stability AS 员工稳定性,
    productivity_rank AS 生产力排名,
    ROUND(active_employees * 100.0 / SUM(active_employees) OVER(), 2) AS 员工占比百分比,
    earliest_hire_date AS 最早入职日期,
    latest_hire_date AS 最近入职日期
FROM strategic_analysis
ORDER BY active_employees DESC, revenue_per_employee DESC;

-- =====================================================================
-- Q3部分查询总结
-- =====================================================================
-- 本SQL文件包含5个核心运营分析查询，解决CEO Jasmine Rivers关注的关键业务问题：
-- 1. St Lucia地区电池业务发展趋势和客户价值分析
-- 2. St Lucia地区承包商网络覆盖和服务质量评估
-- 3. 电气安装工绩效排名和专业化程度分析
-- 4. 高价值服务区域识别和市场竞争力评估
-- 5. 人力资源结构分析和生产力贡献评估
--
-- 所有查询均基于PostgreSQL语法，使用了CTE、窗口函数、聚合分析等高级技术
-- 查询结果将为GigaGlow的运营优化和战略决策提供数据支撑
-- =====================================================================
