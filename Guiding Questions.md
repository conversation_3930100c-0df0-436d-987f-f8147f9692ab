**Guiding Questions** 

The ‘Guiding Questions’ that follow are to be addressed in your Business Consulting Report. This is to 

be formatted **professionally and appropriately**, as discussed in the Assessment Guideline 

document. 

Your report is to have an introduction. In the introduction, discuss the **scope of the engagement**, 

outline your **approach to this engagement**, and **the purpose of your engagement** – that is, why 

you are doing it. 

At all times, **write your business consulting report with reference to the Marking Rubric provided** 

**in the Assessment Guideline. You are to include at least four supporting SQL Scripts you used** 

**in support of your analysis for guiding question 2, 3, and 4.** 

**Question 1: IT Governance Assessment and Recommendations** 

**Requirements:** 

**You are to review the IT governance controls in place at GigaGlow. You are to:** 

1. **Identify** the current IT governance mechanisms in place using the 'Engagement Model' from 

the 'Foundation for Execution' as a guide. It is likely that the 'Minimum IT Governance 

Practices' discussed in the seminar 'IT Governance' will inform you in this task. 

2. **Evaluate** the current IT governance mechanisms (structures, processes, and relational 

mechanisms) as to whether they are effective or ineffective. 

3. **Recommend at least two improvements** to GigaGlow's approach to IT Governance 

From the case description, identify, evaluate, and recommend improvements to GigaGlow's approach 

to IT governance. This should be a section within your Business Consulting Report. These 

recommendations do not need to address *all* IT governance issues, but certainly should address the 

most pressing, prominent, concerns around IT Governance. 

Your assessment and recommendations should consider the context for GigaGlow, and the 

information provided in this specification, as well as any further information you obtain during the 

engagement. 

**Question 2: Assessment of General Controls** 

**Requirements:** 

**You are to review the Internal Controls System (Physical, IT General, and Application Controls:** 

1. **Identify** and **evaluate** the current **physical General** controls in place. It is likely that the 

seminars 'General IT Environment' and 'General Controls' will inform you in this task. 

2. **Identify** and **evaluate** the current **IT General** controls in place. It is likely that the seminars 

'General IT Environment' and 'General Controls' will inform you in this task. 

3. **Identify** and **evaluate** key **application** controls in place. It is likely that the seminar 'Controls 

Testing’ will inform you in this task. (Hint: As a starting point, consider the Sybil system and 

the **authorizations** table.) 

4. **Evaluate** the internal controls system **as a whole**. 
5. **Recommend at least three improvements** to GigaGlow’s IT General Controls, having 

regard to their current context and addressing weakness you identified in your evaluation. 

From the case description, identify, evaluate, and recommend improvements to GigaGlow's approach 

to IT governance. This should be a section within your Business Consulting Report. Again, these 

recommendations do not need to address *all* control issues, but certainly should address the most 

pressing, prominent, concerns around the controls. 

Your assessment and recommendations should consider the context for GigaGlow, and the 

information provided in this specification, as well as any further information you obtain during the 

engagement.







**Question 3: Identify Operational Concerns at GigaGlow** 

**You are to review GigaGlow Operations and assess whether there are any operational concerns** 

**here. You are to:** 

1. **Answer** Jessica’s **operational questions** noted **below**. 
2. **Identify at least two operational concerns** at GigaGlow in relation to the GigaGlow 

approach to delivering services to its clients, including a description of these operational 

concerns. 

3. **Make at least three recommendations** that address these **operational concerns.** All 

identified operational concerns should be addressed by at least one recommendation to 

GigaGlow management. It is likely that you will wish to make multiple recommendations. 

**Operational Questions:** You are to examine the data files provided in relation to GigaGlow**.** 

Jasmine has the **following five operational questions** you are to answer: 

1. How many **customers** of GigaGlow live in the suburb of St Lucia and purchased a **battery** 

**between 2022 and 2025 (inclusive)**? 

2. **How many roof cleaners** (i.e. the external vendors that clean the roof in preparation for 

GigaGlow Glaze) **operate from the suburb of St Lucia**, and **how many different roof** 

**cleaners actually cleaned roofs in St Lucia, between 2022 and 2025 (inclusive)**? 

3. Provide a list of **all electrical installers currently employed by GigaGlow** (i.e. their 

employment is not terminated and thus they have no ‘end_date’ value in the database) and 

the number of electrical installations (i.e., **installation of inverters or batteries)** they have 

made. Include their full addresses in the list. Order the list in descending order by the 

number of installations made – including any that have undertaken **no installations**, if any. 

4. Provide a list of the top 10 suburbs that have been serviced by GigaGlow **by net value****1** **of** 

**installations.** Order the list by total value of services received. 

5. Provide a **list identifying each job and the number of employees in each job at** 

**GigaGlow**. Do not include terminated employees. Order the list by the number of employees 

in each job – in descending order. 

Present these answers in a table in this titled section. You should include any SQL used to answer 

these questions in the appendices. 

**Operational Concerns:** An operational concern can include specific fraud-related controls or focus 

on efficiency and effectiveness. These recommendations should be new – that is, they are separate 

to your recommendations in Questions 1 and 2. 

This question should be addressed in a separate section within your Business Consulting Report. 

You are to examine the data records provided in relation to GigaGlow’s Operations in the **GigaGlow** 

**database.** You should also consider your understanding of the GigaGlow operations. 

The goal of an operational review is to improve its effectiveness (i.e., that the process delivers better 

and improved services) and its efficiency (i.e., opportunities to improve the cost of delivery of services 

– or to deliver the same services as always, but for less resource cost). 

For example, you may wish to examine questions such as: 

• Is the GigaGlow referrals system correctly calculated and paid? 

• Does the referrals system properly address its stated aim of matching customers to local 

cleaners? 

• Can the approach to the organising of the GigaGlow cleaners be improved by ensuring more 

appropriate cleaners? 

You will need to explore the database using SQL. 

Use appendices appropriately. 



1In this context, ‘net value’ means the value of all services recorded in the glaze_sale table less directly

related outgoings (e.g., payments to cleaners etc). Outgoing payments are indicated by negative sale 

amounts in the glaze_sale table.

Your assessment and recommendations should consider the context for GigaGlow, and the 

information provided in this specification, as well as any further information you obtain during the 

engagement.



**Question 4: Undertake a Fraud Review** 

**Requirements:** 

**You are to undertake a Fraud Review and document your activities and your findings in the** 

**Report. Your report should identify:** 

1. The **schedule** of fraud detection techniques undertaken (see the seminar 'Business Ethics 

and Fraud'). 

2. Any **missing techniques** and why they cannot be performed. 
3. Other **ad hoc** indicators of fraud that you identify through exploring the data. 
4. The **results** of the tests (include detailed samples of results and SQL commands used in the 

appendices). 

5. At least two professional **visualized representations** of the results of at SQL test procedures 

used in (4) above (for example, as a histogram, column chart, line chart, or other data 

visualization possible using Excel). 

6. A **broader consideration of fraud** in the context of GigaGlow (e.g., the Fraud Triangle). 
7. Your **conclusions** as to whether fraud is occurring and **recommendations** to address these 

fraud issues. 

You are to perform tests that could reveal the existence of fraud in the company based on the data 

that you have. You should design your tests around the **Fraud Detection Techniques** discussed in 

seminars. This should be presented as a section with that title in your Business Consulting Report. 

The tests performed are to be documented as a schedule in the report. Some techniques may not be 

available due to limitations in the data provided, and these missing techniques should be identified 

together a rationale as to why the technique cannot be used. 

However, these techniques are the **minimum** to be undertaken – you should explore the data 

generally as well as the discussion within this specification document to see if there are other 

indicators of potential fraud. 

**There may be other clues hidden within the data that you need to investigate. Perhaps - a** 

**puzzle worthy of a Roman Emperor? Or is someone cunning enough to try a different** 

**encryption method?**

As part of the professional presentation of this report, you are also to **develop and include at least** 

**two visualized representation of the findings** for the relevant SQL Test Procedures used to review 

fraud. This visualized representation must be created using Excel and be to a professional standard. 

You should provide **at least two recommendations** on how to address these fraud issues. 

Use appendices appropriately. 

Prepared by: Micheal Axelsen 

Senior Lecturer (Business Information Systems) Date: 

