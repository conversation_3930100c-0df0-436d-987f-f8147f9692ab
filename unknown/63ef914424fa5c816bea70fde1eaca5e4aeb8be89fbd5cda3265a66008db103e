**Information Systems** 

GigaGlow 

Assignment Specification



**Purpose** 

This document provides the Assignment Specification for the assessment item “Business Consulting 

Report (IS Recommendations)”. You should refer to the associated Assessment Guideline for the 

Marking Rubric. 

Note: this assignment is an **individual** assignment and will be electronically submitted. You may 

discuss (but not collaborate on) the assignment with your peers. The work you submit should be 

yours, and yours alone. 

**About GigaGlow** 

The sudden decarbonisation of the world economy has created a multitude of business opportunities. 

Central to decarbonisation has been the need to replace fossil fuels with electricity. The normal 

response has been to use solar panels on the roof that will generate electricity and put the power into 

either the grid or a large home battery. 

However, there has been a recent technological innovation – Photovoltaic Paint, or the now-famous 

GigaGlow Glaze. This product is central to GigaGlow’s success. It is exactly what you think it is –

your house is painted with GigaGlow Glaze is applied to make the entire house a Solar Panel. The 

GigaGlow business model has several elements. All prices quoted below are inclusive of GST. 

The first way that GigaGlow makes money is their contractor referrals service. This service matches 

the home-owner to a local roof-cleaner who will clean the roof in preparation for the application of 

GigaGlow Glaze. This requires special training and so only certified roof cleaners are used. 

GigaGlow charges a fee of $100 for this matching service, and collects the fee on behalf of the 

cleaner and passes this on to the cleaner – cleaners charge around $20 per m2 of house area. 

GigaGlow takes a 10% commission on the cleaning fee – so, if a cleaner cleans the roof and charges 

$1000 to do so, GigaGlow collects the $1000 and passes on $900 to the cleaner (and so keeping 

$100 to itself). The amount paid to the cleaner is rounded to the nearest dollar. 

After the roof has been cleaned, GigaGlow will apply the GigaGlow Glaze to the roof if the 

homeowner requests it. After the roof has been painted, the house becomes a V-LiSPA – a Virtual 

Limited Solar Panel Array. GigaGlow Glaze is completely clear, and is made up of colloidal quantum 

dots that are spray painted onto the house. One litre of GigaGlow Glaze covers 10 square metres; 

only one coat is required. GigaGlow charges $55 per square metre to apply it. With the average 

house being 200m2 in area, it costs on average around $11,000 to paint the roof. 

At this point, GigaGlow will install three types of inverters (10kW, 15kW, and 20kW) and three types of 

battery (20kWh, 30kWh, and 40kWh). The three inverters cost $1,650, $2,145, and $2,530 

respectively while the three batteries cost $11,000, $16,500, and $22,000 each respectively. Each 

house must have an inverter for safety reasons (it will create a fire otherwise) if the glaze is applied to 

the roof, and owners may choose to install a home battery although not all do so.

Every installation of GigaGlow requires a yearly safety inspection ($99), and every 4 to 6 years a 

touch-up application of GigaGlow is required costing $25 per square metre. 

GigaGlow has its offices located in Oxley, Brisbane. GigaGlow has a new building purpose-built on the 

eastern side of the corner of Factory Road and Factory Road Loop. 

GigaGlow is a privately owned company, but it has received significant funding from the Clean Energy 

Finance Corporation (CEFC)to improve the take-up of electric batteries in Australia. 

Despite the clear advantages of their product, GigaGlow has been having some difficulties with their 

profitability lately. The company is very successful with its sales but is having trouble remaining 

profitable. 

You have been engaged by the GigaGlow board of directors - at the discreet urging of the Clean 

Energy Finance Corporation - to provide them with consulting advice for improvements to their IT 

governance and IT operations, as well as to undertake a fraud assessment. 

As part of your brief, you are therefore to consider how IT governance can be improved at GigaGlow 

as well as consider operational and fraud issues as part of your IS audit role. You are developing a 

Consulting Report that will be provided to the GigaGlow Board. 

You, as a consultant, are here to help GigaGlow become both more effective *and* efficient. 

You are provided with a SQL data file with system information in it for your analysis as part of your 

review. You are also provided with case notes relating to discussions with key players in this scenario. 

You need this information to answer the Guiding Questions in your Consulting Report, which are at 

the end of this Specification. 

Appendix 1 contains data definition tables, and Appendix 2 contains a view of the Data Diagram.





**Background** 

**Data Files** 

The data files for this assignment are located on website with this Case Description. This is an SQL 

file for uploading via DBeaver. 

The file is called "populate_gigaglow_script V1.sql", and it is provided on Blackboard with this 

assignment specification. 

This is a database population script. It is executed exactly as provided. You will need to ensure that 

the connection is set to your server and that the database is connected to your own already-created 

database ('gigaglow') before running this script. 

When you run this script, you can then run a separate script “gigalow_validation.sql” . You will then be presented with the below information. ***\*Check that your database passes all tests.\****

 

| ***\*Ref\**** | ***\*Table Description\****                              | ***\*Test\**** | ***\*Benchmark\**** | ***\*Pass/Fail\****   |
| ------------- | -------------------------------------------------------- | -------------- | ------------------- | --------------------- |
| ***\*10\****  | ***\*-----ACCOUNTS RECEIVA\*******\*BLE SYSTEM-----\**** |                |                     | ***\*TEST RESULT\**** |
| ***\*20\****  | ***\*customer\****                                       | ***\*1124\**** | ***\*1124\****      | ***\*PASS\****        |
| ***\*30\****  | ***\*-----PAYROLL SYSTEM-----\****                       |                |                     | ***\*TEST RESULT\**** |
| ***\*40\****  | ***\*casual_hourlyrates\****                             | ***\*4\****    | ***\*4\****         | ***\*PASS\****        |
| ***\*50\****  | ***\*employee\****                                       | ***\*272\****  | ***\*272\****       | ***\*PASS\****        |
| ***\*60\****  | ***\*job_position\****                                   | ***\*14\****   | ***\*14\****        | ***\*PASS\****        |
| ***\*70\****  | ***\*payroll\****                                        | ***\*53\****   | ***\*53\****        | ***\*PASS\****        |
| ***\*80\****  | ***\*payroll\*******\*_\*******\*detail\****             | ***\*9063\**** | ***\*9063\****      | ***\*PASS\****        |
| ***\*90\****  | ***\*salaries\****                                       | ***\*26\****   | ***\*26\****        | ***\*PASS\****        |

 



| ***\*100\**** | ***\*standard_hours_log\****                                 | ***\*354\****  | ***\*354\****       | ***\*PASS\****        |
| ------------- | ------------------------------------------------------------ | -------------- | ------------------- | --------------------- |
| ***\*110\**** | ***\*status_lookup\****                                      | ***\*2\****    | ***\*2\****         | ***\*PASS\****        |
| ***\*120\**** | ***\*tax_rates\****                                          | ***\*4\****    | ***\*4\****         | ***\*PASS\****        |
| ***\*130\**** | ***\*-----ACCOUNTS PAYABLE SYSTEM-----\****                  |                |                     | ***\*TEST RESULT\**** |
| ***\*140\**** | ***\*payment\*******\*_\*******\*made\****                   | ***\*956\****  | ***\*956\****       | ***\*PASS\****        |
| ***\*150\**** | ***\*vendor\****                                             | ***\*90\****   | ***\*90\****        | ***\*PASS\****        |
| ***\*160\**** | ***\*vendor\*******\*_\*******\*invoice\****                 | ***\*938\****  | ***\*938\****       | ***\*PASS\****        |
| ***\*170\**** | ***\*-----GIGAGLOW CONTRACTOR REFERR\*******\*ALS SYSTEM-----\**** |                |                     | ***\*TEST RESULT\**** |
| ***\*180\**** | ***\*glaze_sale\****                                         | ***\*3881\**** | ***\*3881\****      | ***\*PASS\****        |
| ***\*Ref\**** | ***\*Table Description\****                                  | ***\*Test\**** | ***\*Benchmark\**** | ***\*Pass/Fail\****   |
| ***\*190\**** | ***\*month\*******\*_\*******\*cleaner\*******\*_\*******\*satisfaction\**** | ***\*292\****  | ***\*292\****       | ***\*PASS\****        |
| ***\*200\**** | ***\*-----SYBIL AUTHORIZATIONS----\****                      |                |                     | ***\*TEST RESULT\**** |
| ***\*210\**** | ***\*authorizations\****                                     | ***\*816\****  | ***\*816\****       | ***\*PASS\****        |
| ***\*220\**** | ***\*backup\*******\*_\*******\*log\****                     | ***\*1096\**** | ***\*1096\****      | ***\*PASS\****        |

An SQL file is provided that is a companion to this Assignment Specification with data for the year

***\*2024\**** on it – this data is to be analysed by you as part of your consulting report.

When this file is executed in PostGRES, an ERD will be created in DBeaver that will show the relationships between data tables.



**IT Services** 

GigaGlow has several key information systems. These systems manage their accounts receivable 

(customers and therefore debtors), accounts payable (suppliers/vendors), payroll, and the contractor 

referrers system. There is also the Backup Log system and the access control system Sybil that 

controls user access to most applications. 

These information systems are mostly all legacy systems developed a long time ago for GigaGlow 

(back when the company used to operate as a ‘just’ a house painting service and before 

the ’Renewables Revolution’ brought on by Jasmine. Although the Board are quite prepared to spend 

and invest into their GigaGlow Glaze product itself, they are determined to recoup their investment in 

developing those legacy systems by using these systems for as long as possible. 

They do not want to spend money on IT as it already costs too much. 

There is an IT support department. There are 10 people currently employed in the GigaGlow IT 

department. The IT Manager is Hillary Smith, and she prides herself on running a tightly knit team. 

Jonno Trez is the current software developer (all software is written in a combination of Visual Cobol, 

Python, and APLX – APLX is a fairly obscure programming language and you have likely never heard 

of it so it might be worth taking a look) and three software maintenance staff (Ria, Hiranya, and Lily), 

as well as database administrator (DBA), Giselle France. 

There are also four IT support maintenance personnel (Jimmy, Ravi, Xiaoying, and Xinyao) who are 

paid the same as the software maintenance staff. The team works as one when GigaGlow is busy 

with projects, and all members of the team pitch in to complete work. Hillary oversees the IT team but 

lets them do their work as they see fit; she relies on Giselle as her assistant manager. 

The IT department has significantly increased as GigaGlow pivoted to implementing GigaGlow Glaze. 

For a long time, there was only Mick Neville, a crusty old software developer who only programmed in 

his two favourite programming systems: Cobol and APLX. All legacy systems are written in Visual 

Cobol and APLX and are the back-office systems that implement the website transactions. 

The new software developer, Jonno Trez, is a relatively new hire, and he develops software principally 

in Python, though has a working knowledge of Visual Cobol and no understanding of APLX at all (who

does?) To address the gap, Mick Neville – the recently retired software developer – is retained on a 

contract of $5,000 per annum to maintain the software code for the legacy systems. 

This usually takes about one day a week. Mick helped Graham Willey – the former CEO – build the 

original systems – the Accounts Receivable, Payroll, Accounts Payable and the GigaGlow Contractor 

Referral system – back in 1983 when GigaGlow was first starting out in the painting business. Mick 

was best mates with Graham Willey, when the company was first built and has never stopped working 

for GigaGlow except for a six months period when Graham Willey abruptly retired from working in the 

business day-to-day and appointed his daughter, Jasmine, as CEO. But in the end it was all fine, 

Mick came back and helped Jasmine pivot the business to GigaGlow and has been very supportive 

up until he retired and beyond. 

The IT team held a retirement BBQ for Mick where he received a $50 JB HiFi voucher and a novelty 

‘World’s Most Awesome Programmer’ coffee cup (featuring Professor Frink from The Simpsons). 

All team members are agile and flexible and ensure that the work is done as required. For example, 

Jimmy is in an IT support role, but has a software development background and regularly works on 

maintaining and updating the payroll system as much as he can. All software development and 

maintenance staff work on the system to ensure that the important applications – like the GigaGlow 

Contractors Referrers system – continue to provide GigaGlow with a competitive advantage. 

Jimmy seems devoted to GigaGlow and rarely – if ever – takes holidays. 

Giselle France is the DBA at GigaGlow, and she helped Mick with building and maintaining the original 

systems when GigaGlow went online. This was back in 2002 through her consulting company, France 

Forward Consulting. She was later hired by GigaGlow directly and continues to help build and 

maintain systems at GigaGlow as well. 

Hillary Smith really relies on Giselle and is sorry that due to the need to reduce costs, Giselle's salary 

– which used to be relatively high, as she gave up her software consulting career to work for 

GigaGlow – has been reduced. Although ostensibly Giselle's hours were reduced as well, Hillary 

knows that Giselle's hours have not really changed much at all. 

**IT Governance** 

GigaGlow is a relatively small company with around 130 full time and casual employees. Jasmine 

Rivers is the Chief Executive Officer, and she makes all decisions. Quinnlyn Yao is the Chief Financial 

Officer, and Yvonne Price runs the sales team as Sales Manager. 

GigaGlow does not have an IT Steering Committee (Jasmine says that "it's only another waste of time 

– besides, it's IT. Not what we do around here – we are not a tech company, we are strategic enablers 

of the Renewables Revolution!"). Jasmine believes that she knows whether a project is worth funding 

'just by looking at it' and besides, ‘business cases are all horse-hockey – not worth the laser printer 

ink they are printed with’. 

Instead, Hillary Smith prepares the IT Budget each year based on the age of the equipment in place 

(usually they aim for new hardware purchases to last for around seven years), and this budget is 

approved by the Executive Team of Jasmine, Quinnlyn, and Yvonne. 

Once a year, Hillary attends the Strategy Day with the Executive Team; Hillary really likes the muffins 

that she gets through that process. Every strategy day, Hillary asks for a budget to remove the creaky, 

old information systems that were developed by Mick – despite the protestations of her team who 

don’t want to learn new software - but Jasmine is adamant that she wants to get her money's worth 

out of GigaGlow's IT.





**Physical Infrastructure, Disaster Recovery and Data Storage** 

GigaGlow has its Data Centre in the basement of its new building in Oxley, Brisbane. It is a new 

building on the eastern side of the corner of Factory Road and Factory Road Loop. Jasmine built a 

dedicated data centre in an unused corner – an old storeroom - of the underground carpark – her 

retired father Graham Willey bought the site several years ago and has built the new building 

specifically for GigaGlow to use, but he forgot to build a proper data centre. 

Jasmine’s office has an amazing view of Oxley Creek and the Archerfield Wetlands. 

Graham bought the site in 2022 – it was much cheaper than any of the other sites nearby. 

There is one UPS (Uninterruptible Power Supply) unit in the server room in the underground carpark 

that is sufficient to power the data centre for three hours in the event of unexpected power outages. 

There is an air conditioning unit in the data centre in the basement, and to save money and the 

GigaGlow carbon footprint this air conditioning unit is powered down after-hours and on weekends. 

Biometric controls lock the room. All members of the senior leadership team and the IT Team have 

access to the data centre, as well as Janie Brightwell, the GigaGlow receptionist. Janie also maintains 

the security logs for the data centre. 

The data centre runs the servers for the information systems used by GigaGlow. These run a 

combination of Linux (Mandrake Corporate Server 3, Linux 2.6.3) and Windows 2000. All information 

systems are now built on PostgreSQL Version 7; they were originally developed using the Ingres 

database management system and Mick ported them to PostgreSQL (since PostGRES is open 

source, he changed the code to be more efficient. 

With a laugh, Giselle notes that she refuses to upgrade any of these systems because it would break 

all the information systems developed for GigaGlow and, if it isn't broken, there is no need to try and 

'fix it'. 

All corporate files, however, are hosted on Dropbox Business. GigaGlow uses Office 365 and Dropbox 

to manage its corporate files. Backups of files in the data centre are made every day, and the log of 

these backups is recorded in the data files provided (see the backup_log table). 

The custom-built accounts receivable, accounts payable, payroll and GigaGlow contractor referral 

systems are automatically zipped each day and stored as an unencrypted file on OneDrive. 

The business continuity plan (BCP) is maintained by Hillary Smith. It was last updated five or six years 

ago when the old office burned down in a fire. 

Hillary regularly tests the BCP by sending a multiple-choice quiz to all staff members on what to do in 

the case of emergency.

**Accounts Receivable System** 

Accounts receivable is the system that records how much customers owe GigaGlow. This is a custom

built system. 

In the data files, you are only provided with the 'customer' table. This system contains information on 

customers, their credit limit and the amount owed by customers. The amount owed by customers 

includes both GigaGlow charges and products stole in the retail store. The retail store is not part of 

this review. 

However, Jasmine tells you that absolutely no customer is allowed to exceed their credit limits – and 

they don’t! She receives regular reports on credit limits from the custom system and maintains a close 

eye on this information. To ensure separation of duties, Janie Brightwell, the receptionist, prints these 

regular reports of customers exceeding their credit limit and provides them to Jasmine. 

**Accounts Payable System** 

Accounts payable is the system that records the money that GigaGlow owes to its creditors. Again, 

this is a custom-built system. It has relationships with the payroll system – particularly in relation to the 

tasks undertaken by different employees in the Finance department. Creditors (vendors) are only paid 

by finance officers. 

In the data files, you are provided with several tables. 

The 'vendor' table records details about vendors, including the company name, address, any notes 

about the vendor, and the amount that GigaGlow owes to the vendor. The amount owed to vendors is 

the total of all amounts owed on each vendor's invoices (i.e., the ‘amount_owing’ field in 

vendor_invoice). Amounts owed to vendors that support the GigaGlow Glaze implementation is not 

recorded in this system. 

Vendor invoices are recorded in the 'vendor_invoice' table. It records any invoices received from 

vendors, including the amount of the invoice, the date the invoice was issued, and how much is left 

owing on the invoice. When the invoice is paid, the 'paid flag' field is set to 'Y'.

Payments to these vendor creditors are recorded in the 'payment_made' table; this table records the 

date the payment was made, the amount paid on the invoice, and the related vendor and invoice. It 

also records the finance officer that processed the payment in the ‘finance_emp_id’ field, which stores 

the employee’s employee number from the employee table. Only finance officers process these 

payments. 

The payment_made table relates to the vendor, vendor_invoice, and employee tables. When a 

payment is made, the amount_owing field in the vendor_invoice table is updated so that the amount 

owing in vendor_invoice matches the amount of the invoice less payments made as recorded in the 

payments_made table. 

Quinnlyn advises that – due to the company's worsening cash position – as Chief Financial Officer 

she has been making sure that invoices are paid in full only when the terms (the number of days 

allowed before the invoice becomes overdue) have been fully utilised; she admits that, occasionally, 

some invoices are paid later than that as cashflow is particularly poor right now. 

This means that sometimes, invoices are only half-paid after 14 days, due to the worsening cash 

position but Quinnlyn is adamant that all invoices are paid within two months.



**Payroll System** 

Payroll is the system that records money paid by GigaGlow to its employees. Again, this is a 

custombuilt system, and it has relationships particularly with the Accounts Payable system (where the 

employee number of the finance officers that make payments to vendors is recorded). 

In the data files, you are provided with several tables. 

The 'employee' table is the centre of the system. This records all employees, their home address, the 

type of job they hold and whether they are full time or part time. The employee's next of kin is also 

identified. The employee table is related to the 'job' table, the 'status_lookup' table, the 'ft_salaries' 

table, the 'payroll' table, the 'standard_hours_log' table, and the ‘payments_made’ table. 

The 'job_position' table provides a unique identifier for each job held at GigaGlow. There are fourteen 

different types of job, including part-time and casual jobs. This table simply provides a description for 

each job. The job table relates to the employee table, the ft_salaries table, and the pt_hourlyrates 

table. 

The 'status_lookup' table simply describes the status code set out in the employee table, to which it 

relates. Employees are either full time (these are salaried staff and do not require standard hours to 

be recorded) or part time (and so standard hours are recorded). 

The 'ft_salaries' table provides an historical listing of the salaries paid to full-time positions. When the 

salary changes for specific jobs, an extra row is added to the ft_salaries table together with the start 

date. For example, job 11 – 'Database Administrator', the position held by Giselle France – started the 

year on a $92,000 annual salary, and this was reduced to $80,000 on 1st July. This information is used 

to determine the total amount paid to individuals in the payroll detail table by dividing the annual 

salary by 52.18 (this calculation turns salary figures into equivalent weekly payments – after allowing 

for leap years). 

Similarly, the 'pt_hourly' rates table records changes to the hourly payments made to part-time 

employees. Again, this table records the date of a rate change for each role. These roles only receive 

pay rises twice a year – once in January, and once in July. 

As with the 'ft_salaries' and 'pt_hourly' tables, the 'standard_hours_log' table records changes to 

standard hours for each employee. The most recent value in the standard_hours_log table matches to 

the current_standard_hours field in the employee table. 

The payroll table summarises the details of all pay runs. Note that the ft_salaries, pt_hourly, and the 

standard_hours_log tables are applied as at the date of each payroll – so for example, if a salary is 

changed on or before the date of the payroll, then that salary applies according to the rules in the 

ft_salaries table. The payroll table records the payroll number, the date of the payment, the amount 

paid to all employees after tax, the tax paid, and the total salaries in that pay run. These amounts 

summate their respective fields in the 'payroll_details' table. The payroll table relates only to the 

payroll_detail table. 

The 'payroll_detail' table records the net payment, taxation withheld, and the total payment made to

each individual employee. The sum of the three figures recorded (net payment, taxation, and total

payment) equate to the respective value in the payroll table for each pay run (i.e., total_net, total_tax, 

and total_salaries in the payroll table respectively). Note that net payment is the total payment less 

the taxation amount. 

This table relates to the employee table, the status_lookup table, and the payroll table. 

The taxation amount is determined by reference to the tax_rates table. This is a progressive tax 

system. For example, a person earning $1,000 in a week will pay 0% tax on earnings up to $348.79, 

then 19% tax on earnings between $348.79 to $862.42, and 33% tax on earnings exceeding $862.42 

(all the way up to the next threshold). For a $1,000 payment, therefore, an employee would pay 0% of 

$348.79, 19% of ($862.42 - $348.79), and 33% of ($1,000 - $862.42) = 0 + $97.59 + $45.40 = 

$142.99 in taxation. 

Each week Quinnlyn Fisher asks Janie Brightwell, the receptionist, to prepare the electronic report for 

the payroll and then Quinnlyn signs off on the payment made. Janie is asked to prepare this to ensure 

that reporting duties are kept separate from the transaction recording duties of the finance officers 

working with Quinnlyn.



**GigaGlow Contractor Referrers System** 

The GigaGlow Contractor Referrers System is another bespoke (custom) system at GigaGlow. This 

system tracks all referrals made to contractors and scores their performance by customer feedback. 

Each contractor is referred to a client by casual sales staff, and a ‘referrer’s fee’ of $20 is paid to the 

casual sales staff at GigaGlow who make the referral when the contractor’s overall customer 

satisfaction score is greater than 80% (scored out of 5) each month, but only when the contractor 

carries out at least 5 cleans that month. 

In the data files, you are provided with two tables: ‘glaze_sale’ and ‘month_cleaner_satisfaction’ . 

The glaze_sale table shows the customer, the date the service was ordered, which external vendor is 

supporting the process (the roof cleaner, as applicable), internal employers supporting the process 

(e.g. electrical installers), and the person who referred the roof cleaner. The roof area of the house 

and the value of the sale transaction is also recorded as is the type of service/sale provided in the 

‘sale_type’ field. The type of service or sale has several codes and each sale progresses through 

these services:

**Matching-Fee:** This sale represents the $100 fee charged by GigaGlow to customers to 

match them with a suitable cleaner in their local area. Here, the salesperson organised the 

sale (recorded in emp_id) and the person who referred the cleaner to the client is recorded in 

referrer_emp_id). The referrer is the person who receives the $20 allowance if the customer 

is satisfied. The vendor_id is the vendor cleaning the roof. 

• **Cleaning-Fee:** Next, the cleaner (see vendor_id) cleans the roof – the same salesperson is 

recorded as is the referrer. The fee here (sale_amount) varies according to the charge by the 

painter and the area of roof. The satisfaction score is recorded out of 5 from a survey 

administered by the sales person to the customer (satisfaction_score). 

• **Paid-To-Cleaner:** This records the amount paid to the cleaner by GigaGlow – it should be 

the cleaning fee less the 10% commission that GigaGlow retains for services rendered. The 

sales person is recorded as well (emp_id). No referrer is recorded at this stage, and the 

amount recorded is negative to indicate money paid to the external vendor. 

• **Referral-Fee:** This is the amount paid to the referrer of the external cleaner if the vendor 

referred has at least 5 cleans in the month and an average of 4 or more customer 

satisfaction. This information is summarised in the month_cleaner_satisfaction table. 

• **Glaze:** This code indicates the charge made to the customer for placing GigaGlow Glaze on 

the roof by GigaGlow’s casual roof painter (emp_id). GigaGlow Glaze is optional and not all 

customers proceed to the glaze after the roof has been cleaned. No external vendor and no 

referrer is recorded in this step. The amount charged for glazing varies by roof area as 

discussed previously (sale_amount). 

• **Inverter:** Every customer must have an inverter installed in their home if the Glaze has been 

applied. This work is carried out by an electrical installer (emp_id). The amount charged for 

the inverter depends on the size of the inverter installed, which is usually dependent on the 

area of roof painted in Glaze (sale_amount)

**Battery:** Every customer that has had an inverter installed in their home may choose to 

install a battery. This work is carried out by an electrical installer (emp_id). The amount 

charged for the battery depends on the size of the inverter installed, which is usually 

dependent on the area of roof painted in Glaze (sale_amount). 

• **Yearly-Inspection:** Each year the installation must be reviewed and inspected by a 

GigaGlow employee. The charge for this yearly inspection is $99 paid annually. 

The month_cleaner_satisfaction table summarises the satisfaction scores provided by home owners 

for the service received from external vendors when cleaning the roof. This table has a record for 

each month and each vendor active in that month. It records the vendor (vendor_id), the year 

(clean_year) and month (clean_month) and the total number of cleans in that month 

(count_of_cleans). Finally the table records the average satisfaction score indicated by home owners 

in that month in relation to the vendor’s work (average_satisfaction_score). 

The individual who referred the cleaner will receive a $20 referral fee if that cleaner was used at least 

five times in a month and the average satisfaction is 4 out of 5, or greater. 

This system was built by Mick shortly before his retirement party, and he still maintains this software.



**Sybil Authorisations Control System** 

The Sybil Authorisations Control Control System provides the authorisation table for several different 

systems. Sybil identifies all employees that can approve vendor invoices for payment, and records all 

those who approve actual payments made (see the vendor_invoice.approver_emp_id and 

payment_made.approver_emp_id fields). 

According to the authorizations table, only the indicated employees are to approve records in the 

identified table (where the approve_rights field is true). 

You may be able to look at the authorizations table to validate your assumptions about how each of 

the different information systems works.



**Consulting with Clients over Coffee** 

You seek an informal discussion with Jasmine 

Rivers, the CEO. 

You arrive early for the first day of this engagement 

at GoNCharge’s offices at Rocklea. You wait for 

Janie Brightwell, the receptionist, to finish her phone 

call. You overhear part of her conversation: “OK 

Mick, see you then. I’ll get that milk on the way 

home.” 

Janie turns to you with a large smile, and says 

“Honestly, you’d think that since he’s retired my old 

Uncle Mick could sort out his own dairy products! 

Anyway, what can I do for you?” 

You explain that you are here to meet with Jasmine 

for a cup of coffee. 

Janie places a quick call to Jasmine to let her know 

you are here and directs you through the reception 

doors to the kitchenette.





The kitchenette is at the back of the GigaGlow office 

building and shortly Jasmine joins you. Jasmine is a 

person without too many airs and graces. She offers 

you a cup of coffee, and you accept your cup of 

International Roast coffee meekly





Jasmine pulls up a chair to the table, invites you to 

sit, and starts to talk to you – she looks at you 

intently over the rim of her coffee cup. 

"I don't get it," she says. "We are at our most 

competitive ever, in the strongest and 

fastestgrowing market ever, and I am having 

difficulty finding the cash to pay our suppliers – it 

just doesn't add up. Confidentially – I'd like you to 

do some sniffing around. This fraud review – I'll be 

surprised if there's nothing wrong here, but I can't 

for the life of me think what is going on. I trust all 

these people with everything – well, perhaps 

except for those painting contractors. They’re 

always looking for an angle and a way to make 

money. Perhaps they’re ripping us off." 

She sips her coffee; you can see where a little glob 

of undissolved International Roast slides down the 

side of her cup. 

“My dad never liked me taking us down this road –

but there’s so much money to be made and it’s so 

much more interesting.” She continues. I never 

quite understood it – he’s into computers, and was 

always tinkering with the computers with old Mick. 

But he didn’t build a proper data centre for me 

back when he built this building. Ah well.” 

You see the glob of coffee fall onto the counter top 

where it sits, unnoticed. 

“I’m also concerned about the performance of 

those painters – we have a lot of them on the 

books, we have a great performance incentive in 

place, but honestly it feels like we’re just being too darn sloppy with the orders these days. Too much 

wasted time where we visit the houses and discover that the roof isn’t properly prepared for GigaGlow 

Glaze.” 

"If you could go through the data looking for evidence of bad stuff happening – that'd be great. I'm a 

big believer that we can over-complicate things – I don't need a 10-page business case telling me 

whether something's a good investment. I have a nose for business, I know what's going to work. I 

don't need committees and things." 

"Well. At least, I don't think I do – perhaps you'll have some way to convince me. But if we are going 

to go formal with bureaucracy around here, it's going to be a real strain on our big happy family. I'd 

like some recommendations that tighten things up without making our lives impossible." Jasmine 

takes another sip of her coffee and outlines the questions she wants you to answer. 

When Jasmine has outlined her questions, she excuses herself and says you should ask Janie to 

show you around the building. “Janie’s amazing, she’s great, a real chip off her Uncle’s block. She 

seems to be able to get everything to work as of when we need it done. Very glad that she has gone 

to live with her uncle in his dotage.” 

You return to reception and ask Janie to walk you around. She gladly accepts. “You know, it’s great 

that someone’s finally looking at these computer systems – my uncle thinks it’s high time. But I don’t 

know – if we go to new computer systems, I’ll find it really hard to get my work done. Between you 

and me, Uncle Mick has helped me out a bit. He has given me a couple of handy AI prompts that I 

can use to help me get the information out of the systems that I need. Really helps with those payroll 

reports and paying our people and vendors each month.” She reflects a bit on what she’s just said, 

and says, a little nervously: “Don’t tell Jasmine that though! She’ll think I’m cheating when I use AI to 

do my job. But it’s OK, Uncle Mick set it up for me."



The rest of the morning is spent uneventfully wandering around. About the only thing of note was 

when Janie let you into the data centre in the basement to have a look. You both surprise Uncle Mick 

as he is doing something on the main server. 

Now, about those questions…