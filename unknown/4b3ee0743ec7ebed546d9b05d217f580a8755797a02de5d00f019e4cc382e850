# Appendix 1: Data Definition Tables

## AUTHORIZATIONS

| Column Name       | Data Type   | Constraints           | Description                                        |
| ----------------- | ----------- | --------------------- | -------------------------------------------------- |
| authorizations_id | serial4     | NOT NULL, PRIMARY KEY | Unique identifier for each authorization record    |
| emp_id            | int4        | NULL                  | Employee ID associated with the authorization      |
| table_name        | varchar(30) | NULL                  | Name of the table for which authorization is given |
| approve_rights    | bool        | NULL                  | Indicates if the employee has approval rights      |

This table provides a clear and concise definition of each column in the authorizations table. The primary key is authorizations_id, and there is a foreign key constraint referencing the emp_id in the public.employee table.

## BACKUP_LOG

| Column Name   | Data Type | Constraints           | Description                                                  |
| ------------- | --------- | --------------------- | ------------------------------------------------------------ |
| backup_log_id | serial4   | NOT NULL, PRIMARY KEY | Unique identifier for each backup log record                 |
| backup_date   | timestamp | NULL                  | Date and time when the backup was performed                  |
| status_code   | text      | NULL                  | Status code indicating the result of the backup ('SUCCESS' or 'FAIL') |

This table provides a clear and concise definition of each column in the backup_log table. The primary key is backup_log_id.

## CASUAL_HOURLY_RATES

| Column Name        | Data Type     | Constraints                        | Description                                                  |
| ------------------ | ------------- | ---------------------------------- | ------------------------------------------------------------ |
| job_position_id    | int4          | NOT NULL, PRIMARY KEY, FOREIGN KEY | Unique identifier for the job position, references public.job_position(job_position_id) |
| start_date         | date          | NOT NULL, PRIMARY KEY              | The date from which the hourly rate is effective             |
| casual_hourly_rate | numeric(18,2) | NULL                               | The hourly rate for casual employees                         |

This table provides a clear and concise definition of each column in the casual_hourlyrates table. The primary key is a composite key consisting of job_position_id and start_date.

## CUSTOMER

| Column Name     | Data Type     | Constraints           | Description                                                  |
| --------------- | ------------- | --------------------- | ------------------------------------------------------------ |
| customer_id     | serial4       | NOT NULL, PRIMARY KEY | Unique identifier for each customer                          |
| customer_name   | varchar(80)   | NULL                  | Name of the customer                                         |
| street          | varchar(40)   | NULL                  | Street address of the customer                               |
| suburb          | varchar(40)   | NULL                  | Suburb of the customer                                       |
| city            | varchar(40)   | NULL                  | City of the customer                                         |
| state           | varchar(10)   | NULL                  | State of the customer                                        |
| post_code       | varchar(10)   | NULL                  | Postal code of the customer                                  |
| amount_owed     | numeric(18,2) | NULL                  | Amount owed by the customer                                  |
| notes           | varchar(100)  | NULL                  | Additional notes about the customer                          |
| credit_limit    | numeric(18,2) | NULL                  | Credit limit for the customer                                |
| customer_emp_id | int4          | NULL, FOREIGN KEY     | Employee ID associated with the customer, references public.employee(emp_id) |

This table provides a clear and concise definition of each column in the customer table. The primary key is customer_id.

## EMPLOYEE

| Column Name            | Data Type    | Constraints           | Description                                                  |
| ---------------------- | ------------ | --------------------- | ------------------------------------------------------------ |
| emp_id                 | serial4      | NOT NULL, PRIMARY KEY | Unique identifier for each employee                          |
| first_name             | varchar(50)  | NULL                  | First name of the employee                                   |
| last_name              | varchar(50)  | NULL                  | Last name of the employee                                    |
| address1               | varchar(100) | NULL                  | Primary address line of the employee                         |
| address2               | varchar(100) | NULL                  | Secondary address line of the employee                       |
| city                   | varchar(30)  | NULL                  | City of the employee                                         |
| state                  | varchar(10)  | NULL                  | State of the employee                                        |
| post_code              | varchar(10)  | NULL                  | Postal code of the employee                                  |
| phone                  | varchar(40)  | NULL                  | Phone number of the employee                                 |
| start_date             | date         | NULL                  | Start date of employment                                     |
| end_date               | date         | NULL                  | End date of employment                                       |
| job_position_id        | int4         | NULL, FOREIGN KEY     | Job position ID, references public.job_position(job_position_id) |
| status_code            | varchar(2)   | NULL, FOREIGN KEY     | Status code, references public.status_lookup(status_code)    |
| current_standard_hours | int4         | NULL                  | Current standard working hours                               |
| next_of_kin            | varchar(100) | NULL                  | Next of kin information                                      |

This table provides a clear and concise definition of each column in the employee table. The primary key is emp_id.

## GLAZE_SALE

| Column Name        | Data Type     | Constraints           | Description                                                  |
| ------------------ | ------------- | --------------------- | ------------------------------------------------------------ |
| glaze_sale_id      | serial4       | NOT NULL, PRIMARY KEY | Unique identifier for each glaze sale record                 |
| customer_id        | int8          | NULL, FOREIGN KEY     | Customer ID associated with the sale, references public.customer(customer_id) |
| date_ordered       | timestamp     | NULL                  | Date and time when the sale was ordered                      |
| vendor_id          | int4          | NULL, FOREIGN KEY     | Vendor ID associated with the sale, references public.vendor(vendor_id) |
| emp_id             | int4          | NULL, FOREIGN KEY     | Employee ID who made the sale, references public.employee(emp_id) |
| referrer_emp_id    | int4          | NULL, FOREIGN KEY     | Employee ID who referred the sale, references public.employee(emp_id) |
| house_area         | int4          | NULL                  | Area of the house involved in the sale                       |
| sale_amount        | numeric(18,2) | NULL                  | Amount of the sale                                           |
| sale_type          | varchar(20)   | NULL                  | Type of the sale                                             |
| satisfaction_score | int4          | NULL                  | Customer satisfaction score for the sale                     |

This table provides a clear and concise definition of each column in the glaze_sale table. The primary key is glaze_sale_id.

## JOB_POSITION

| Column Name     | Data Type    | Constraints           | Description                             |
| --------------- | ------------ | --------------------- | --------------------------------------- |
| job_position_id | int4         | NOT NULL, PRIMARY KEY | Unique identifier for each job position |
| position_title  | varchar(100) | NULL                  | Title of the job position               |

This table provides a clear and concise definition of each column in the job_position table. The primary key is job_position_id.

## MONTH_CLEANER_SATISFACTION

| Column Name                   | Data Type     | Constraints           | Description                                                  |
| ----------------------------- | ------------- | --------------------- | ------------------------------------------------------------ |
| month_cleaner_satisfaction_id | serial4       | NOT NULL, PRIMARY KEY | Unique identifier for each monthly cleaner satisfaction record |
| vendor_id                     | int4          | NULL, FOREIGN KEY     | Vendor ID associated with the satisfaction record, references public.vendor(vendor_id) |
| clean_year                    | int8          | NULL                  | Year of the cleaning service                                 |
| clean_month                   | int4          | NULL                  | Month of the cleaning service                                |
| count_of_cleans               | int4          | NULL                  | Number of cleans performed in the month                      |
| average_satisfaction_score    | numeric(18,2) | NULL                  | Average satisfaction score for the cleans                    |

This table provides a clear and concise definition of each column in the month_cleaner_satisfaction table. The primary key is month_cleaner_satisfaction_id.

## PAYMENT_MADE

| Column Name       | Data Type     | Constraints           | Description                                                  |
| ----------------- | ------------- | --------------------- | ------------------------------------------------------------ |
| payment_made_id   | serial4       | NOT NULL, PRIMARY KEY | Unique identifier for each payment made record               |
| vendor_id         | int4          | NULL, FOREIGN KEY     | Vendor ID associated with the payment, references public.vendor(vendor_id) |
| payment_date      | date          | NULL                  | Date when the payment was made                               |
| vendor_invoice_id | int4          | NULL, FOREIGN KEY     | Vendor invoice ID associated with the payment, references public.vendor_invoice(vendor_invoice_id) |
| amount_paid       | numeric(18,2) | NULL                  | Amount paid                                                  |
| finance_emp_id    | int4          | NULL, FOREIGN KEY     | Employee ID of the finance employee who processed the payment, references public.employee(emp_id) |
| approver_emp_id   | int4          | NULL, FOREIGN KEY     | Employee ID of the approver, references public.employee(emp_id) |

This table provides a clear and concise definition of each column in the payment_made table. The primary key is payment_made_id.

## PAYROLL

| Column Name    | Data Type     | Constraints           | Description                                  |
| -------------- | ------------- | --------------------- | -------------------------------------------- |
| payroll_id     | serial4       | NOT NULL, PRIMARY KEY | Unique identifier for each payroll record    |
| pay_date       | date          | NULL                  | Date when the payroll was processed          |
| total_net      | numeric(18,2) | NULL                  | Total net amount paid                        |
| total_tax      | numeric(18,2) | NULL                  | Total tax amount deducted                    |
| total_salaries | numeric(18,2) | NULL                  | Total amount of salaries                     |
| paid           | varchar(1)    | NULL                  | Indicates if the payroll has been paid (Y/N) |

This table provides a clear and concise definition of each column in the payroll table. The primary key is payroll_id.

## PAYROLL_DETAIL

| Column Name       | Data Type     | Constraints           | Description                                                  |
| ----------------- | ------------- | --------------------- | ------------------------------------------------------------ |
| payroll_detail_id | serial4       | NOT NULL, PRIMARY KEY | Unique identifier for each payroll detail record             |
| payroll_id        | int4          | NULL, FOREIGN KEY     | Payroll ID associated with the detail, references public.payroll(payroll_id) |
| pay_date          | date          | NULL                  | Date when the payroll was processed                          |
| emp_id            | int4          | NULL, FOREIGN KEY     | Employee ID associated with the payroll detail, references public.employee(emp_id) |
| status            | varchar(2)    | NULL, FOREIGN KEY     | Status code, references public.status_lookup(status_code)    |
| job_position_id   | int4          | NULL, FOREIGN KEY     | Job position ID, references public.job_position(job_position_id) |
| standard_hours    | int4          | NULL                  | Standard working hours                                       |
| net_payment       | numeric(18,2) | NULL                  | Net payment amount                                           |
| taxation          | numeric(18,2) | NULL                  | Taxation amount                                              |
| total_payment     | numeric(18,2) | NULL                  | Total payment amount                                         |
| approver_emp_id   | int4          | NULL, FOREIGN KEY     | Employee ID of the approver, references public.employee(emp_id) |

This table provides a clear and concise definition of each column in the payroll_detail table. The primary key is payroll_detail_id.

## SALARIES

| Column Name     | Data Type     | Constraints                        | Description                                                  |
| --------------- | ------------- | ---------------------------------- | ------------------------------------------------------------ |
| job_position_id | int4          | NOT NULL, PRIMARY KEY, FOREIGN KEY | Unique identifier for the job position, references public.job_position(job_position_id) |
| start_date      | date          | NOT NULL, PRIMARY KEY              | The date from which the annual salary is effective           |
| annual_salary   | numeric(18,2) | NULL                               | The annual salary for the job position                       |

This table provides a clear and concise definition of each column in the salaries table. The primary key is a composite key consisting of job_position_id and start_date.

## STANDARD_HOURS_LOG

| Column Name    | Data Type | Constraints                        | Description                                                  |
| -------------- | --------- | ---------------------------------- | ------------------------------------------------------------ |
| emp_id         | int4      | NOT NULL, PRIMARY KEY, FOREIGN KEY | Unique identifier for the employee, references public.employee(emp_id) |
| start_date     | date      | NOT NULL, PRIMARY KEY              | The date from which the standard hours are effective         |
| standard_hours | int4      | NULL                               | The standard working hours for the employee                  |

This table provides a clear and concise definition of each column in the standard_hours_log table. The primary key is a composite key consisting of emp_id and start_date.

## STATUS_LOOKUP

| Column Name | Data Type   | Constraints           | Description                         |
| ----------- | ----------- | --------------------- | ----------------------------------- |
| status_code | varchar(2)  | NOT NULL, PRIMARY KEY | Unique code representing the status |
| description | varchar(30) | NULL                  | Description of the status           |

This table provides a clear and concise definition of each column in the status_lookup table. The primary key is status_code.

## TAX_RATES

| Column Name | Data Type     | Constraints           | Description                                |
| ----------- | ------------- | --------------------- | ------------------------------------------ |
| gtr_id      | serial4       | NOT NULL, PRIMARY KEY | Unique identifier for each tax rate record |
| threshold   | numeric(18,2) | NULL                  | Income threshold for the tax rate          |
| rate        | numeric(18,2) | NULL                  | Tax rate applicable above the threshold    |

This table provides a clear and concise definition of each column in the tax_rates table. The primary key is gtr_id.

## VENDOR

| Column Name  | Data Type     | Constraints           | Description                       |
| ------------ | ------------- | --------------------- | --------------------------------- |
| vendor_id    | serial4       | NOT NULL, PRIMARY KEY | Unique identifier for each vendor |
| vendor_name  | varchar(80)   | NULL                  | Name of the vendor                |
| street       | varchar(30)   | NULL                  | Street address of the vendor      |
| suburb       | varchar(30)   | NULL                  | Suburb of the vendor              |
| city         | varchar(30)   | NULL                  | City of the vendor                |
| state        | varchar(10)   | NULL                  | State of the vendor               |
| post_code    | varchar(10)   | NULL                  | Postal code of the vendor         |
| vendor_type  | varchar(2)    | NULL                  | Type of the vendor                |
| notes        | varchar(100)  | NULL                  | Additional notes about the vendor |
| amount_owing | numeric(18,2) | NULL                  | Amount owed to the vendor         |

This table provides a clear and concise definition of each column in the vendor table. The primary key is vendor_id.

## VENDOR_INVOICE

| Column Name       | Data Type     | Constraints           | Description                                                  |
| ----------------- | ------------- | --------------------- | ------------------------------------------------------------ |
| vendor_invoice_id | serial4       | NOT NULL, PRIMARY KEY | Unique identifier for each vendor invoice record             |
| vendor_id         | int4          | NULL, FOREIGN KEY     | Vendor ID associated with the invoice, references public.vendor(vendor_id) |
| invoice_id        | varchar(25)   | NULL                  | Invoice identifier                                           |
| invoice_date      | date          | NULL                  | Date of the invoice                                          |
| amount            | numeric(18,2) | NULL                  | Total amount of the invoice                                  |
| amount_owing      | numeric(18,2) | NULL                  | Amount still owed on the invoice                             |
| paid_flag         | varchar(1)    | NULL                  | Flag indicating if the invoice is paid (Y/N)                 |
| approver_emp_id   | int4          | NULL, FOREIGN KEY     | Employee ID of the approver, references public.employee(emp_id) |

This table provides a clear and concise definition of each column in the vendor_invoice table. The primary key is vendor_invoice_id.